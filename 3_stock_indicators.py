import numpy as np
import pandas as pd
import talib as ta
from scipy import stats
#from stock_data_reader import get_stock_data
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
import time
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import queue
import threading
import os
import sys
from serverchan_sdk import sc_send
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification
from trade_calendar import is_trade_day, get_latest_trade_day
import argparse  # 添加参数解析
from scipy.signal import savgol_filter
from MyTT import EMA  # 导入MyTT库中的EMA函数

# 资源配置类
class ResourceConfig:
    """资源使用配置管理"""

    # 数据库连接池配置
    DB_POOL_SIZE = 20           # 基础连接池大小
    DB_MAX_OVERFLOW = 50        # 最大溢出连接数
    DB_POOL_TIMEOUT = 60        # 连接超时时间
    DB_POOL_RECYCLE = 3600      # 连接回收时间

    # 并发处理配置
    MAX_WORKERS_MULTIPLIER = 2  # CPU核心数的倍数
    MAX_WORKERS_LIMIT = 32      # 最大工作进程数限制
    MIN_WORKERS = 2             # 最小工作进程数

    # 批处理配置
    DB_BATCH_SIZE = 20000       # 数据库批量写入大小
    QUEUE_TIMEOUT = 120         # 队列超时时间
    WRITER_THREAD_TIMEOUT = 600 # 写入线程超时时间

    @classmethod
    def get_optimal_workers(cls, cpu_count=None, custom_workers=None):
        """计算最优工作进程数"""
        if custom_workers:
            return max(cls.MIN_WORKERS, min(custom_workers, cls.MAX_WORKERS_LIMIT))

        if cpu_count is None:
            cpu_count = mp.cpu_count()

        # 对于CPU密集型任务，使用CPU核心数的倍数
        optimal = cpu_count * cls.MAX_WORKERS_MULTIPLIER
        return max(cls.MIN_WORKERS, min(optimal, cls.MAX_WORKERS_LIMIT))

    @classmethod
    def get_db_pool_config(cls):
        """获取数据库连接池配置"""
        return {
            'pool_size': cls.DB_POOL_SIZE,
            'max_overflow': cls.DB_MAX_OVERFLOW,
            'pool_timeout': cls.DB_POOL_TIMEOUT,
            'pool_recycle': cls.DB_POOL_RECYCLE,
            'pool_pre_ping': True,
            'echo': False
        }

# 全局数据库引擎
_DB_ENGINE = None

def get_db_engine():
    """获取数据库引擎（单例模式）"""
    global _DB_ENGINE
    if _DB_ENGINE is None:
        DB_URL = "mysql+pymysql://root:31490600@**********:3306/instockdb"
        _DB_ENGINE = create_engine(DB_URL, **ResourceConfig.get_db_pool_config())
    return _DB_ENGINE

def read_stock_data_from_db(stock_code, start_date=None, end_date=None):
    """
    从数据库中读取指定股票代码和日期区间的数据
    :param stock_code: 股票代码,如'000001'
    :param start_date: 开始日期,格式为'YYYY-MM-DD',可选
    :param end_date: 结束日期,格式为'YYYY-MM-DD',可选
    :return: 返回查询结果,如果没有找到返回None
    """
    try:
        engine = get_db_engine()

        # 使用参数化查询防止SQL注入
        query = """
            SELECT trade_date, stock_code, name, close, open, high, low, vol as volume
            FROM ts_stock_daily
            WHERE stock_code = :stock_code
        """

        params = {'stock_code': stock_code}

        # 添加日期区间条件
        if start_date and end_date:
            query += " AND trade_date BETWEEN :start_date AND :end_date"
            params.update({'start_date': start_date, 'end_date': end_date})
        elif start_date:
            query += " AND trade_date >= :start_date"
            params['start_date'] = start_date
        elif end_date:
            query += " AND trade_date <= :end_date"
            params['end_date'] = end_date

        # 按日期排序
        query += " ORDER BY trade_date DESC"

        # 执行查询
        with engine.connect() as conn:
            df = pd.read_sql(text(query), conn, params=params)

        # 返回查询结果
        if not df.empty:
            return df
        else:
            log_warning(logger, f"未找到股票 {stock_code} 在指定日期区间的数据")
            return None
    except Exception as e:
        log_error(logger, f"从数据库读取数据失败: {str(e)}")
        return None

def get_stock_data(stock_code, start_date=None, end_date=None):
    """
    获取股票数据的封装函数
    :param stock_code: 股票代码
    :param start_date: 开始日期(可选)
    :param end_date: 结束日期(可选)
    :return: 返回股票数据DataFrame
    """
    # 调用优化后的 read_stock_data_from_db 函数
    result = read_stock_data_from_db(stock_code, start_date, end_date)
    if result is not None:
        return result
    else:
        return pd.DataFrame()  # 返回空DataFrame

# 获取当前脚本的路径和文件名
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# ServerChan配置
SENDKEY = "sctp5099tkrhuzdrsrepnwn352fyftq"

# def is_not_st(name):
#     """检查是否为非ST股票"""
#     return not str(name).upper().startswith(('*ST', 'ST'))

def is_valid_price(price):
    """检查价格是否有效"""
    if isinstance(price, str):
        return price != '-'
    return not pd.isna(price) and float(price) > 0

def calculate_slope(series, period):
    """Calculate linear regression slope for a given series and period"""
    return pd.Series(series).rolling(period).apply(
        lambda x: np.polyfit(range(len(x)), x, deg=1)[0] if len(x) == period else np.nan, 
        raw=True
    ).values

def get_all_stock_codes():
    """
    Get all stock codes from the database

    Returns:
    --------
    list
        List of stock codes
    """
    try:
        engine = get_db_engine()
        query = "SELECT DISTINCT stock_code FROM stock_basic ORDER BY stock_code"

        with engine.connect() as conn:
            df = pd.read_sql(text(query), conn)

        log_progress(logger, f"获取到 {len(df)} 个股票代码")
        return df['stock_code'].tolist()
    except Exception as e:
        log_error(logger, f"Error getting stock codes: {str(e)}")
        return []

def get_valid_stock_codes():
    """
    获取有效的股票代码，过滤掉ST股票、价格无效的股票、数据量不足的股票和价格波动太小的股票

    Returns:
    --------
    list
        有效股票代码列表
    """
    try:
        engine = get_db_engine()
        
        # 查询股票，且最新价格有效的股票，并且日线数据数量大于等于100条
        # 注意：SQL中的%需要用%%转义，避免被误解为Python字符串格式化占位符
        # 已注释ST股票过滤：WHERE b.name NOT LIKE '%%ST%%' AND b.name NOT LIKE '*%%'
        query = """
            SELECT
                b.stock_code,
                b.name,
                d.close,
                COUNT(daily.trade_date) as total_count,
                SUM(CASE WHEN daily.close > 0 AND daily.close IS NOT NULL THEN 1 ELSE 0 END) as valid_count,
                MAX(daily.close) as max_price,
                MIN(daily.close) as min_price,
                AVG(daily.close) as avg_price,
                (MAX(daily.close) - MIN(daily.close)) / AVG(daily.close) as price_variation_ratio
            FROM stock_basic b
            JOIN (
                SELECT stock_code, close
                FROM ts_stock_daily
                WHERE trade_date = (SELECT MAX(trade_date) FROM ts_stock_daily)
            ) d ON b.stock_code = d.stock_code
            JOIN ts_stock_daily daily ON b.stock_code = daily.stock_code
            WHERE d.close > 0 AND d.close IS NOT NULL
            GROUP BY b.stock_code, b.name, d.close
            HAVING 
                COUNT(daily.trade_date) >= 100
                AND SUM(CASE WHEN daily.close > 0 AND daily.close IS NOT NULL THEN 1 ELSE 0 END) >= 100
                AND (MAX(daily.close) - MIN(daily.close)) / AVG(daily.close) >= 0.01
        """
        
        with engine.connect() as conn:
            df = pd.read_sql(text(query), conn)

        if df.empty:
            log_warning(logger, "未找到有效股票")
            return []
        
        # 打印数据统计
        total_stocks = len(df)
        avg_total_count = df['total_count'].mean()
        min_total_count = df['total_count'].min()
        avg_valid_count = df['valid_count'].mean()
        min_valid_count = df['valid_count'].min()
        avg_price_variation = df['price_variation_ratio'].mean()
        min_price_variation = df['price_variation_ratio'].min()
        
        log_progress(logger, f"找到 {total_stocks} 只有效股票 (非ST、价格有效、有效数据点≥100、价格波动≥1%)")
        log_progress(logger, f"数据统计: 总数据量: 平均{avg_total_count:.1f}条, 最少{min_total_count}条")
        log_progress(logger, f"有效数据量: 平均{avg_valid_count:.1f}条, 最少{min_valid_count}条")
        log_progress(logger, f"价格波动率: 平均{avg_price_variation:.2%}, 最小{min_price_variation:.2%}")
        
        # 查看被过滤掉的股票数量
        # 1. 查询股票总数（已注释ST过滤）
        # total_non_st_query = "SELECT COUNT(DISTINCT stock_code) as count FROM stock_basic WHERE name NOT LIKE '%%ST%%' AND name NOT LIKE '*%%'"
        total_non_st_query = "SELECT COUNT(DISTINCT stock_code) as count FROM stock_basic"
        
        # 2. 查询总数据量足够但有效数据点不足的股票数（已注释ST过滤）
        insufficient_valid_data_query = """
            SELECT COUNT(*) as count
            FROM (
                SELECT
                    b.stock_code,
                    COUNT(daily.trade_date) as total_count,
                    SUM(CASE WHEN daily.close > 0 AND daily.close IS NOT NULL THEN 1 ELSE 0 END) as valid_count
                FROM stock_basic b
                JOIN ts_stock_daily daily ON b.stock_code = daily.stock_code
                GROUP BY b.stock_code
                HAVING
                    COUNT(daily.trade_date) >= 100
                    AND SUM(CASE WHEN daily.close > 0 AND daily.close IS NOT NULL THEN 1 ELSE 0 END) < 100
            ) as tmp
        """
        
        # 3. 查询数据量不足100条的股票数
        insufficient_data_query = """
            SELECT COUNT(*) as count
            FROM (
                SELECT 
                    b.stock_code, 
                    COUNT(daily.trade_date) as data_count
                FROM stock_basic b
                LEFT JOIN ts_stock_daily daily ON b.stock_code = daily.stock_code
                WHERE b.name NOT LIKE '%%ST%%' AND b.name NOT LIKE '*%%'
                GROUP BY b.stock_code
                HAVING COUNT(daily.trade_date) < 100 OR COUNT(daily.trade_date) IS NULL
            ) as tmp
        """
        
        # 4. 查询数据量足够但波动不足的股票数
        insufficient_variation_query = """
            SELECT COUNT(*) as count
            FROM (
                SELECT 
                    b.stock_code, 
                    (MAX(daily.close) - MIN(daily.close)) / AVG(daily.close) as price_variation_ratio,
                    COUNT(daily.trade_date) as total_count,
                    SUM(CASE WHEN daily.close > 0 AND daily.close IS NOT NULL THEN 1 ELSE 0 END) as valid_count
                FROM stock_basic b
                JOIN ts_stock_daily daily ON b.stock_code = daily.stock_code
                WHERE b.name NOT LIKE '%%ST%%' AND b.name NOT LIKE '*%%'
                GROUP BY b.stock_code
                HAVING 
                    COUNT(daily.trade_date) >= 100
                    AND SUM(CASE WHEN daily.close > 0 AND daily.close IS NOT NULL THEN 1 ELSE 0 END) >= 100
                    AND (MAX(daily.close) - MIN(daily.close)) / AVG(daily.close) < 0.01
            ) as tmp
        """
        
        with engine.connect() as conn:
            # 获取非ST股票总数
            total_result = conn.execute(text(total_non_st_query))
            total_non_st_stocks = total_result.scalar() or 0
            
            # 获取总数据量足够但有效数据点不足的股票数
            valid_data_result = conn.execute(text(insufficient_valid_data_query))
            insufficient_valid_data_stocks = valid_data_result.scalar() or 0
            
            # 获取数据量不足的股票数
            data_result = conn.execute(text(insufficient_data_query))
            insufficient_data_stocks = data_result.scalar() or 0
            
            # 获取数据量足够但波动不足的股票数
            variation_result = conn.execute(text(insufficient_variation_query))
            insufficient_variation_stocks = variation_result.scalar() or 0
            
            # 计算有效价格股票数
            valid_price_stocks = total_non_st_stocks - insufficient_data_stocks - insufficient_valid_data_stocks
            filtered_by_price = valid_price_stocks - total_stocks - insufficient_variation_stocks
            
            if filtered_by_price < 0:
                filtered_by_price = 0  # 确保不会出现负数
            
            logger.info(f"过滤统计:")
            logger.info(f"- 总股票数: {total_non_st_stocks} 只")  # 已注释ST过滤
            logger.info(f"- 因总数据量不足100条被过滤: {insufficient_data_stocks} 只 ({insufficient_data_stocks/total_non_st_stocks*100:.1f}%%)")
            logger.info(f"- 因有效数据点不足100个被过滤: {insufficient_valid_data_stocks} 只 ({insufficient_valid_data_stocks/total_non_st_stocks*100:.1f}%%)")
            logger.info(f"- 因价格波动不足被过滤: {insufficient_variation_stocks} 只 ({insufficient_variation_stocks/total_non_st_stocks*100:.1f}%%)")
            logger.info(f"- 因其他原因被过滤(无效价格等): {filtered_by_price} 只 ({filtered_by_price/total_non_st_stocks*100:.1f}%%)")
        
        return df['stock_code'].tolist()
    except Exception as e:
        logger.error(f"获取有效股票代码时出错: {str(e)}")
        # 打印更详细的错误信息
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return []

def check_latest_data(db_url):
    """
    检查数据库中的日线数据是否存在最新数据缺失的情况
    
    :param db_url: 数据库连接URL
    :return: 返回一个元组 (缺少最新数据的股票列表, 最新日期)
    """
    engine = None
    try:
        engine = create_engine(db_url, pool_size=5, max_overflow=10, pool_recycle=3600)
        
        # 获取最新的交易日期
        latest_date_query = "SELECT MAX(trade_date) as latest_date FROM ts_stock_daily"
        with engine.connect() as conn:
            latest_date_df = pd.read_sql(latest_date_query, conn)
            latest_date = latest_date_df['latest_date'].iloc[0]
        
        if latest_date:
            # 查询上次最新日期的记录数
            latest_count_query = f"SELECT COUNT(DISTINCT stock_code) as stock_count FROM ts_stock_daily WHERE trade_date = '{latest_date}'"
            
            # 查询上次前一交易日的记录数
            # 找到上一个交易日
            prev_date_query = f"""
                SELECT MAX(trade_date) as prev_date 
                FROM ts_stock_daily 
                WHERE trade_date < '{latest_date}'
            """
            
            with engine.connect() as conn:
                latest_count_df = pd.read_sql(latest_count_query, conn)
                prev_date_df = pd.read_sql(prev_date_query, conn)
                
                latest_count = latest_count_df['stock_count'].iloc[0]
                
                if not prev_date_df.empty and not pd.isna(prev_date_df['prev_date'].iloc[0]):
                    prev_date = prev_date_df['prev_date'].iloc[0]
                    
                    # 获取前一天的记录数
                    prev_count_query = f"SELECT COUNT(DISTINCT stock_code) as stock_count FROM ts_stock_daily WHERE trade_date = '{prev_date}'"
                    prev_count_df = pd.read_sql(prev_count_query, conn)
                    prev_count = prev_count_df['stock_count'].iloc[0]
                    
                    # 如果最新日期的股票数量明显少于前一天，可能有部分数据缺失
                    if latest_count < prev_count * 0.95:  # 如果少于前一天的95%
                        log_warning(logger, f"最新日期 {latest_date} 的股票数量 ({latest_count}) 明显少于前一天 {prev_date} 的数量 ({prev_count})，可能有数据缺失")
                        
                        # 查找哪些股票在前一天有数据但在最新日期没有数据
                        missing_stocks_query = f"""
                            SELECT a.stock_code, a.name
                            FROM ts_stock_daily a
                            WHERE a.trade_date = '{prev_date}'
                            AND NOT EXISTS (
                                SELECT 1 FROM ts_stock_daily b
                                WHERE b.trade_date = '{latest_date}'
                                AND b.stock_code = a.stock_code
                            )
                        """
                        
                        missing_stocks_df = pd.read_sql(missing_stocks_query, conn)
                        
                        if not missing_stocks_df.empty:
                            return missing_stocks_df, latest_date
        
        return pd.DataFrame(), latest_date
        
    except Exception as e:
        log_error(logger, f"检查最新数据时出错: {str(e)}")
        return pd.DataFrame(), None
    finally:
        if engine:
            engine.dispose()

def batch_save_indicators_to_db(data_queue, db_url, batch_size=None):
    """Batch save indicators to database from a queue"""
    if batch_size is None:
        batch_size = ResourceConfig.DB_BATCH_SIZE

    # 使用优化的连接池配置
    engine = create_engine(db_url, **ResourceConfig.get_db_pool_config())
    all_data = []  # 存储所有数据
    total_saved = 0

    try:
        while True:
            try:
                df = data_queue.get(timeout=ResourceConfig.QUEUE_TIMEOUT)
                if df is None:
                    break

                df = df.rename(columns={'trade_date': 'date', 'stock_code': 'code'})
                df['date'] = pd.to_datetime(df['date']).dt.date
                all_data.append(df)

            except queue.Empty:
                break

        if all_data:
            # 合并所有数据
            combined_df = pd.concat(all_data, ignore_index=True)

            # 删除现有记录
            codes = combined_df['code'].unique()
            dates = combined_df['date'].unique()
            with engine.begin() as conn:
                delete_query = text("""
                    DELETE FROM cn_stock_indicators
                    WHERE code IN :codes
                    AND date IN :dates
                """)
                conn.execute(delete_query, {"codes": tuple(codes), "dates": tuple(dates)})

            # 一次性写入所有数据，使用更大的批处理大小
            combined_df.to_sql('cn_stock_indicators', engine,
                             if_exists='append',
                             index=False,
                             method='multi',
                             chunksize=batch_size)

            total_saved = len(combined_df)
            log_progress(logger, f"Saved all {total_saved} records to database with batch size {batch_size}")

    except Exception as e:
        log_error(logger, f"Error in batch save process: {str(e)}")
    finally:
        log_progress(logger, f"Batch save process completed. Total saved: {total_saved}")
        if engine:
            engine.dispose()

def calculate_indicators(stock_code, start_date=None, end_date=None):
    """Calculate technical indicators for a given stock"""
    try:
        if start_date:
            start_date_dt = datetime.strptime(start_date, '%Y-%m-%d')
            extended_start_date = (start_date_dt - timedelta(days=250)).strftime('%Y-%m-%d')
        else:
            extended_start_date = None
            
        df = get_stock_data(stock_code, extended_start_date, end_date)
        if df.empty:
            reason = "无日线数据"
            log_warning(logger, f"No data returned for stock {stock_code}")
            return pd.DataFrame(), reason
        
        # 过滤ST股票（已注释）
        # if not is_not_st(df['name'].iloc[0]):
        #     reason = f"ST股票: {df['name'].iloc[0]}"
        #     log_progress(logger, f"Skipping ST stock: {stock_code} - {df['name'].iloc[0]}")
        #     return pd.DataFrame(), reason
        
        # 过滤无效价格的股票
        if not is_valid_price(df['close'].iloc[0]):
            reason = "价格无效"
            log_progress(logger, f"Skipping stock with invalid price: {stock_code}")
            return pd.DataFrame(), reason
        
        if len(df) < 100:
            reason = f"日线数据不足: {len(df)}天，最低需要100天"
            log_warning(logger, f"Insufficient data for stock {stock_code}: {len(df)} days")
            return pd.DataFrame(), reason
        
        # 检查无效价格的比例
        invalid_prices = df['close'].isnull().sum() + (df['close'] == 0).sum()
        if invalid_prices / len(df) > 0.1:  # 如果超过10%的价格数据无效
            reason = f"无效价格比例过高: {invalid_prices}/{len(df)} ({invalid_prices/len(df)*100:.1f}%)"
            log_warning(logger, f"Too many invalid prices for stock {stock_code}: {invalid_prices}/{len(df)} ({invalid_prices/len(df)*100:.1f}%)")
            return pd.DataFrame(), reason
        
        # 检查价格变动是否足够 - 如果价格几乎不变动，指标可能无意义
        price_range = df['close'].max() - df['close'].min()
        price_mean = df['close'].mean()
        if price_mean > 0 and price_range / price_mean < 0.01:  # 如果价格变动小于平均价格的1%
            reason = f"价格变动太小: 范围={price_range:.4f}, 平均={price_mean:.4f}, 比例={price_range/price_mean:.4f}"
            log_warning(logger, f"Price variation too small for stock {stock_code}: range={price_range:.4f}, mean={price_mean:.4f}, ratio={price_range/price_mean:.4f}")
            return pd.DataFrame(), reason
        
        # Ensure we have the required columns
        required_columns = ['trade_date', 'stock_code', 'name', 'open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            missing_cols = [col for col in required_columns if col not in df.columns]
            raise ValueError(f"Missing required columns in stock data: {missing_cols}")
        
        # Create a copy to avoid modifying original data
        data = df.copy()
        
        # Convert trade_date to datetime if it's not already
        if not pd.api.types.is_datetime64_any_dtype(data['trade_date']):
            data['trade_date'] = pd.to_datetime(data['trade_date'])
        
        # Sort data by date in ascending order for proper calculation
        data = data.sort_values('trade_date', ascending=True)
        
        # Convert price columns to float type if they aren't already
        price_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in price_cols:
            data[col] = pd.to_numeric(data[col], errors='coerce')
        
        # Calculate MACD indicators
        try:
            macd, macds, _ = ta.MACD(data['close'].values,
                                   fastperiod=13,
                                   slowperiod=26,
                                   signalperiod=9)
            data['macd'] = macd
            data['macds'] = macds
            data['diff_26d'] = data['macd'].diff()
            data['macd_diff2'] = data['macd'].shift(1)
            data['macd_diff1'] = data['macd']
        except Exception as e:
            log_error(logger, f"Error calculating MACD for {stock_code}: {str(e)}")
            return pd.DataFrame(), f"MACD计算失败: {str(e)}"
        
        
        # 批量计算EMA指标
        try:
            close_values = data['close'].values

            # 使用MyTT库的EMA函数替代TA-Lib
            data['ma26'] = EMA(close_values, 26)
            data['ma100'] = EMA(close_values, 100)
            data['ma_26d'] = data['ma26'].diff()

            # 计算短期和长期EMA的差值，使用MyTT库计算保持一致性
            short_term_ema = EMA(close_values, 13)
            long_term_ema = EMA(close_values, 26)
            data['ema_diff'] = short_term_ema - long_term_ema
            data['diff0_price'] = (data['macd_diff1'] + long_term_ema*(25/27) - short_term_ema*(6/7)) * (189/13)

        except Exception as e:
            log_error(logger, f"Error calculating EMA for {stock_code}: {str(e)}")
            return pd.DataFrame(), f"EMA计算失败: {str(e)}"


        # 对差值应用Savitzky-Golay滤波器平滑
        try:
            # 确保窗口大小为奇数且不超过数据长度
            window_length = 67  # 与 cn_indicator_analysis_echarts.py 保持一致
            if window_length > len(data['ema_diff']):
                window_length = len(data['ema_diff']) if len(data['ema_diff']) % 2 == 1 else len(data['ema_diff']) - 1
                if window_length < 3:
                    window_length = 3
            
            # 使用0阶平滑，与 cn_indicator_analysis_echarts.py 保持一致
            poly_order = 0
            
            # 确保数据没有NaN或无穷大
            ema_diff_clean = np.array(data['ema_diff'].fillna(0).replace([np.inf, -np.inf], 0), dtype=np.float64)
            
            # 添加微小扰动避免完全平坦区域导致SVD计算错误
            ema_diff_clean += np.random.normal(0, 1e-10, len(ema_diff_clean))
            
            try:
                # 尝试使用0阶多项式
                data['ema_diff_smooth66'] = savgol_filter(ema_diff_clean, window_length, poly_order)
            except Exception as e:
                log_warning(logger, f"0阶Savitzky-Golay滤波失败: {str(e)}，尝试使用1阶")
                # 如果0阶失败，尝试1阶
                try:
                    data['ema_diff_smooth66'] = savgol_filter(ema_diff_clean, window_length, 1)
                except Exception as e:
                    log_error(logger, f"1阶Savitzky-Golay滤波也失败: {str(e)}，使用移动平均作为替代")
                    # 最后使用简单移动平均作为后备方案
                    data['ema_diff_smooth66'] = data['ema_diff'].rolling(window=13, min_periods=1).mean()
            
        except Exception as e:
            log_error(logger, f"Savitzky-Golay 滤波计算失败: {str(e)}")
            # 使用简单移动平均作为后备方案
            data['ema_diff_smooth66'] = data['ema_diff'].rolling(window=13, min_periods=1).mean()
        
        # Calculate ATR indicators
        data['atr'] = ta.ATR(data['high'].values, 
                             data['low'].values, 
                             data['close'].values, 
                             timeperiod=14)
        data['atrma5'] = ta.MA(data['atr'].values, timeperiod=5)
        
        # Calculate TRIX indicators
        data['trix'] = ta.TRIX(data['close'].values, timeperiod=26)
        data['trix_diff'] = data['trix'].diff()
        data['trix_26'] = ta.MA(data['trix'].values, timeperiod=66)
        data['trix_26_diff'] = data['trix_26'].diff()
        
        # Calculate Slope indicators
        data['ln_close'] = np.log(data['close'])
        data['slope'] = calculate_slope(data['ln_close'], 90)
        data['slope_1'] = data['slope'].shift(1)
        data['exp_slope'] = np.exp(data['slope'])
        data['power_slope'] = np.power(data['exp_slope'], 250) - 1
        
        # Calculate R-squared and slope90
        def calculate_r2_slope(series):
            if len(series) < 90:
                return np.nan
            series = series.values
            x = np.arange(len(series))
            if np.any(np.isnan(series)):
                return np.nan
            r_value, _ = stats.pearsonr(series, x)
            return r_value ** 2
        
        data['r2_slope'] = data['ln_close'].rolling(90, min_periods=90).apply(calculate_r2_slope)
        data['slope90'] = data['power_slope'] * data['r2_slope']
        
        # Fill NaN values with 0 after all calculations are done
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        data[numeric_columns] = data[numeric_columns].fillna(0)
        
        # Select and return only the requested columns
        columns = ['trade_date', 'stock_code', 'name', 'close', 'macd', 'macds', 'ma26', 'atr', 
                   'ma100', 'slope90', 'atrma5', 'slope', 'power_slope', 'r2_slope',
                   'trix', 'trix_26', 'trix_diff', 'trix_26_diff', 'slope_1', 
                   'ma_26d', 'diff_26d', 'macd_diff1', 'macd_diff2', 'ema_diff_smooth66', 'diff0_price']
        
        result = data[columns]
        
        # Filter to requested date range if start_date was provided
        if start_date:
            start_date_dt = pd.to_datetime(start_date)
            result = result[result['trade_date'] >= start_date_dt]
        
        # Sort the result by date in descending order before returning
        result = result.sort_values('trade_date', ascending=False)
        return result, None  # 返回结果和None表示没有跳过原因
        
    except Exception as e:
        log_error(logger, f"Error calculating indicators for {stock_code}: {str(e)}")
        return pd.DataFrame(), str(e)

def process_stock(stock_code, start_date, end_date):
    """Process a single stock and return its indicators"""
    try:
        log_progress(logger, f"Processing stock: {stock_code}")
        indicators_df, skip_reason = calculate_indicators(stock_code, start_date, end_date)
        return indicators_df, skip_reason
    except Exception as e:
        log_error(logger, f"Error processing stock {stock_code}: {str(e)}")
        return pd.DataFrame(), str(e)

def process_all_stocks(start_date=None, end_date=None, db_url=None, max_workers=None, limit=None):
    """处理所有股票数据,计算指标"""
    if db_url is None:
        db_url = "mysql+pymysql://root:31490600@**********:3306/instockdb"

    # 优化工作进程数量计算
    cpu_count = mp.cpu_count()
    optimal_workers = ResourceConfig.get_optimal_workers(cpu_count, max_workers)

    log_progress(logger, f"系统资源配置:")
    log_progress(logger, f"- CPU核心数: {cpu_count}")
    log_progress(logger, f"- 工作进程数: {optimal_workers}")
    log_progress(logger, f"- 数据库连接池: {ResourceConfig.DB_POOL_SIZE} + {ResourceConfig.DB_MAX_OVERFLOW}")
    log_progress(logger, f"- 批处理大小: {ResourceConfig.DB_BATCH_SIZE}")

    try:
        start_time = time.time()
        
        # 检查数据库中是否有缺失最新日期的数据
        log_progress(logger, "检查数据库中日线数据的完整性...")
        missing_stocks_df, latest_date = check_latest_data(db_url)
        
        if not missing_stocks_df.empty:
            # 记录存在数据缺失
            formatted_date = latest_date.strftime('%Y-%m-%d') if isinstance(latest_date, datetime) else str(latest_date)
            log_warning(logger, f"发现 {len(missing_stocks_df)} 只股票在最新日期 {formatted_date} 缺失数据")
            
            # 显示部分缺失的股票
            missing_count = len(missing_stocks_df)
            sample_size = min(20, missing_count)  # 减少默认显示的缺失股票数量
            missing_samples = missing_stocks_df.head(sample_size)
            
            missing_desc = "\n".join([
                f"- {row['stock_code']} ({row['name']})"
                for _, row in missing_samples.iterrows()
            ])
            log_warning(logger, f"部分缺失最新数据的股票(前{sample_size}个):\n{missing_desc}")
            
            # 发送告警通知
            send_notification(
                message=(
                    f"日线数据检查发现问题:\n"
                    f"共有 {missing_count} 只股票在最新日期 {formatted_date} 没有数据\n\n"
                    f"部分缺失数据的股票(前{sample_size}个):\n{missing_desc}\n\n"
                    f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                ),
                title="日线数据检查警告 - 部分股票缺失最新数据",
                tags="日线数据|警告"
            )
        else:
            if latest_date:
                formatted_date = latest_date.strftime('%Y-%m-%d') if isinstance(latest_date, datetime) else str(latest_date)
                log_progress(logger, f"数据库日线数据完整性检查通过，最新日期 {formatted_date} 的数据完整")
            else:
                log_warning(logger, "无法确定数据库中日线数据的最新日期")
        
        # 获取所有有效股票代码
        stock_codes = get_valid_stock_codes()
        if not stock_codes:
            raise ValueError("没有获取到有效股票代码列表")

        # 如果指定了limit，则限制处理的股票数量
        if limit and limit > 0:
            stock_codes = stock_codes[:limit]
            log_progress(logger, f"限制处理股票数量为: {limit}")

        log_progress(logger, f"将处理 {len(stock_codes)} 只有效股票")
        
        # 初始化错误列表和处理结果
        error_stocks = []
        processed_stocks = []
        skipped_stocks = []
        skip_reasons = {}  # 记录每只被跳过股票的具体原因
        
        # 验证日期格式
        try:
            if start_date:
                datetime.strptime(start_date, '%Y-%m-%d')
            if end_date:
                datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError as e:
            error_msg = f"Invalid date format: {str(e)}"
            log_error(logger, error_msg)
            send_notification(
                message=f"错误信息:{error_msg}",
                title="股票指标计算失败",
                tags="股票指标|失败"
            )
            return
        
        data_queue = mp.Manager().Queue()
        writer_thread = threading.Thread(
            target=batch_save_indicators_to_db,
            args=(data_queue, db_url, ResourceConfig.DB_BATCH_SIZE),
            daemon=True
        )
        writer_thread.start()

        total_processed = 0
        successful_processed = 0

        with ProcessPoolExecutor(max_workers=optimal_workers) as executor:
            future_to_stock = {
                executor.submit(process_stock, code, start_date, end_date): code 
                for code in stock_codes
            }
            
            for future in as_completed(future_to_stock):
                stock_code = future_to_stock[future]
                try:
                    indicators_df, skip_reason = future.result()
                    
                    if indicators_df is None:  # 处理失败
                        error_stocks.append((stock_code, "处理返回None"))
                    elif indicators_df.empty:  # 空数据(可能是ST股票或无效价格)
                        skipped_stocks.append(stock_code)
                        if skip_reason:  # 记录跳过原因
                            skip_reasons[stock_code] = skip_reason
                    else:  # 成功处理
                        # 验证计算结果
                        if indicators_df['macd'].isna().all():
                            error_reason = "MACD计算结果全为NaN，可能是数据不足或数据质量问题"
                            error_stocks.append((stock_code, error_reason))
                            skip_reasons[stock_code] = error_reason
                        elif (indicators_df['macd'] == 0).all():
                            error_reason = "MACD计算结果全为0，可能是股票价格波动极小"
                            error_stocks.append((stock_code, error_reason))
                            skip_reasons[stock_code] = error_reason
                        elif indicators_df['ma26'].isna().all():
                            error_reason = "MA26计算结果全为NaN"
                            error_stocks.append((stock_code, error_reason))
                            skip_reasons[stock_code] = error_reason
                        elif (indicators_df['ma26'] == 0).all():
                            error_reason = "MA26计算结果全为0"
                            error_stocks.append((stock_code, error_reason))
                            skip_reasons[stock_code] = error_reason
                        else:
                            data_queue.put(indicators_df)
                            successful_processed += 1
                            processed_stocks.append(stock_code)
                    
                    total_processed += 1
                    
                    if total_processed % 100 == 0:
                        elapsed_time = time.time() - start_time
                        avg_time_per_stock = elapsed_time / total_processed
                        remaining_stocks = len(stock_codes) - total_processed
                        estimated_remaining_time = remaining_stocks * avg_time_per_stock
                        
                        log_progress(logger,
                            f"Progress: {total_processed}/{len(stock_codes)} stocks "
                            f"({total_processed/len(stock_codes)*100:.1f}%) "
                            f"[{elapsed_time/60:.1f}min elapsed, "
                            f"{estimated_remaining_time/60:.1f}min remaining]\n"
                            f"Successful: {successful_processed}, "
                            f"Skipped: {len(skipped_stocks)}, "
                            f"Failed: {len(error_stocks)}"
                        )
                    
                except Exception as e:
                    error_msg = f"Error processing stock {stock_code}: {str(e)}"
                    log_error(logger, error_msg)
                    error_stocks.append((stock_code, str(e)))
        
        # 等待所有数据写入完成
        data_queue.put(None)
        writer_thread.join(timeout=ResourceConfig.WRITER_THREAD_TIMEOUT)
        
        total_time = time.time() - start_time
        completion_msg = (
            f"\nProcessing completed!\n"
            f"Total stocks processed: {total_processed}\n"
            f"Successfully processed: {successful_processed}\n"
            f"Skipped stocks: {len(skipped_stocks)}\n"
            f"Failed stocks: {len(error_stocks)}\n"
            f"Total time taken: {total_time/60:.2f} minutes\n"
            f"Average time per stock: {total_time/total_processed:.2f} seconds"
        )
        log_progress(logger, completion_msg)
        
        # 显示失败的股票及原因
        if len(error_stocks) > 0:
            error_details = "\nFailed stocks with reasons:\n"
            for stock_code, error_reason in error_stocks:
                error_details += f"- {stock_code}: {error_reason}\n"
            
            logger.error(error_details)
            
        # 汇总跳过原因统计
        if skip_reasons:
            reason_counts = {}
            for reason in skip_reasons.values():
                reason_counts[reason] = reason_counts.get(reason, 0) + 1
            
            # 按照频率排序
            sorted_reasons = sorted(reason_counts.items(), key=lambda x: x[1], reverse=True)
            
            skip_summary = "\nSkipped stocks reason summary:\n"
            for reason, count in sorted_reasons:
                skip_summary += f"- {reason}: {count} stocks\n"
            
            logger.info(skip_summary)
            
            # 展示部分每种原因的具体股票
            skip_details = "\nSelected skipped stocks by reason:\n"
            for reason, _ in sorted_reasons:
                # 找出最多5个该原因的股票
                stocks_with_reason = [code for code, r in skip_reasons.items() if r == reason][:5]
                if stocks_with_reason:
                    skip_details += f"\n{reason}:\n"
                    for code in stocks_with_reason:
                        skip_details += f"- {code}\n"
            
            logger.info(skip_details)
        
        # 发送处理结果通知
        if len(error_stocks) > 0 or len(skipped_stocks) > 0:
            desp = (
                f"处理时间:{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"总处理股票数:{total_processed}\n"
                f"成功处理数:{successful_processed}\n"
                f"跳过股票数:{len(skipped_stocks)}\n"
                f"失败数量:{len(error_stocks)}\n"
                f"总耗时:{total_time/60:.2f}分钟\n"
                f"平均每只股票耗时:{total_time/total_processed:.2f}秒\n\n"
            )
            
            # 添加跳过原因统计
            if skip_reasons:
                desp += "\n跳过原因统计:\n"
                for reason, count in sorted_reasons:
                    desp += f"- {reason}: {count} 只股票\n"
            
            if skipped_stocks:
                desp += f"\n跳过的股票示例(前10个):\n"
                for code in skipped_stocks[:10]:
                    reason = skip_reasons.get(code, "未知原因")
                    desp += f"- {code}: {reason}\n"
                if len(skipped_stocks) > 10:
                    desp += f"... 还有 {len(skipped_stocks) - 10} 个股票被跳过\n"
            
            if error_stocks:
                desp += "\n失败股票列表(全部):\n"
                for stock_code, error in error_stocks:
                    desp += f"- {stock_code}: {error}\n"
            
            title = "股票指标计算"
            if len(error_stocks) > 0:
                title += "出现错误"
                tags = "股票指标|错误"
            else:
                title += "完成（存在跳过的股票）"
                tags = "股票指标|完成"
                
            send_notification(
                message=desp,
                title=title, 
                tags=tags
            )
        
    except Exception as e:
        error_msg = f"Critical error in process_all_stocks: {str(e)}"
        log_error(logger, error_msg)
        send_notification(
            message=f"错误信息:{error_msg}\n时间:{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            title="股票指标计算发生严重错误", 
            tags="股票指标|严重错误"
        )

if __name__ == "__main__":
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='计算股票技术指标')
    parser.add_argument('--force', action='store_true', help='强制使用最近交易日的数据进行计算')
    parser.add_argument('--date', type=str, help='指定计算日期,格式为YYYY-MM-DD')
    parser.add_argument('--start_date', type=str, help='指定计算开始日期,格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, help='指定计算结束日期,格式为YYYY-MM-DD')
    parser.add_argument('--limit', type=int, help='限制处理的股票数量（用于测试）')
    parser.add_argument('--max_workers', type=int, help='指定最大工作进程数')
    parser.add_argument('--batch_size', type=int, help='指定数据库批处理大小')
    args = parser.parse_args()
    
    # 动态调整资源配置
    if args.batch_size:
        ResourceConfig.DB_BATCH_SIZE = args.batch_size
        log_progress(logger, f"自定义批处理大小: {args.batch_size}")

    # 获取当天日期
    today = datetime.now().date()

    # 处理时间段计算
    if args.start_date and args.end_date:
        try:
            start_date = datetime.strptime(args.start_date, '%Y-%m-%d').date().strftime('%Y-%m-%d')
            end_date = datetime.strptime(args.end_date, '%Y-%m-%d').date().strftime('%Y-%m-%d')
            log_progress(logger, f"将计算从 {start_date} 到 {end_date} 的股票指标数据")
            
            # 执行计算
            process_all_stocks(
                start_date=start_date,
                end_date=end_date,
                max_workers=args.max_workers,
                limit=args.limit
            )
            exit(0)
        except ValueError:
            log_error(logger, f"日期格式错误: 应为YYYY-MM-DD格式")
            exit(1)
    
    # 确定要使用的日期
    if args.date:
        try:
            calc_date = datetime.strptime(args.date, '%Y-%m-%d').date()
            log_progress(logger, f"使用指定日期: {calc_date}")
            
            # 检查指定日期是否为交易日
            is_specified_date_trade = is_trade_day(calc_date)
            log_progress(logger, f"指定日期 {calc_date} {'是' if is_specified_date_trade else '不是'}交易日")
            
            # 如果指定日期不是交易日，获取该日期之前的最近交易日
            if not is_specified_date_trade:
                calc_date = get_latest_trade_day(calc_date)
                log_progress(logger, f"指定日期不是交易日，将使用最近交易日: {calc_date}")
        except ValueError:
            log_error(logger, f"日期格式错误: {args.date},应为YYYY-MM-DD格式")
            exit(1)
    else:
        # 检查今天是否为交易日
        is_today_trade = is_trade_day(today)
        log_progress(logger, f"今天 {today} {'是' if is_today_trade else '不是'}交易日")
        
        # 获取最近的交易日
        latest_trade_day = get_latest_trade_day(today)
        log_progress(logger, f"最近的交易日是: {latest_trade_day}")
        
        # 默认情况下使用最近的交易日
        calc_date = latest_trade_day
        
        # 如果今天是交易日且没有指定force，则使用今天的日期
        if is_today_trade and not args.force:
            calc_date = today
            log_progress(logger, f"今天是交易日，将使用今天 {calc_date} 的数据")
        else:
            log_progress(logger, f"将使用最近交易日 {calc_date} 的数据进行计算")
    
    # 转换日期格式
    start_date = end_date = calc_date.strftime('%Y-%m-%d')
    log_progress(logger, f"开始计算 {start_date} 的股票指标数据")
    
    # 执行计算
    process_all_stocks(
        start_date=start_date,
        end_date=end_date,
        max_workers=args.max_workers,
        limit=args.limit
    )