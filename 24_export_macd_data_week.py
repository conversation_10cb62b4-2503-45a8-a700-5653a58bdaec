import pandas as pd
from sqlalchemy import create_engine, text
from contextlib import contextmanager
from datetime import datetime, timedelta
import os
# 使用集中式日志配置
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification
import sys
import argparse

# 添加项目根目录到路径,以便导入 trade_calendar
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from trade_calendar import is_trade_day, get_latest_trade_day, get_friday_of_current_week

# 获取当前脚本的文件名
script_name = os.path.splitext(os.path.basename(__file__))[0]
# 初始化日志系统
logger = setup_logger(script_name)

# 定义列名映射
COLUMN_MAPPING = {
    'date': '交易日期',
    'code': '证券代码',
    'name': '证券简称',
    'close': '当天收盘价',
    'diff_1': 'DIFF_1低',
    'diff_2': 'DIFF_2低',
    'diff1_date': '日期-1低',
    'diff2_date': '日期-2低',
    'diff1_price': '收盘价-1低',
    'diff2_price': '收盘价-2低',
    'jd': '背离判断',
    'd_td': '距离d周数',
    'ma_26d': '周线趋向',
    'diff_26d': 'DIFF趋向',
    'huitiao': '回调',
    # 以下字段已注释，不再需要
    # 'macd_diff1': '26D_DIFF1',
    # 'macd_diff2': '26D_DIFF2',
    'dmd': '现值和最近低值涨幅',
    'diff10_min': 'DIFF_10_MIN',
    'diff10_date': 'DIFF10_DATE',
    'df_10_td': 'DF_10_TD',
    # 以下字段已注释，不再需要
    # 'ma100': 'ma100',
    # 'atr': 'atr',
    # 'atrma5': 'atrma5',
    # 'slope': 'slope',
    # 'slope-1': 'slope-1',
    # 'power_slope': 'power_slope',
    # 'r2_slope': 'r2_slope',
    # 'slope90': 'slope90',
    # 'trix_26': 'trix_26',
    # 'trix_diff': 'trix_26与上一周差值',
    # 'trix': 'trix_66',
    # 'trix_26_diff': 'trix_66与上一周差值',
    # 'ema_diff_smooth66': 'EMA(13-26)的66周0阶平滑'
}

# 定义列的顺序
COLUMN_ORDER = [
    '交易日期', '证券代码', '证券简称', '当天收盘价', 'DIFF_1低', 'DIFF_2低',
    '日期-1低', '日期-2低', '收盘价-1低', '收盘价-2低', '背离判断', '距离d周数',
    '周线趋向', 'DIFF趋向', '回调',  '现值和最近低值涨幅',
    'DIFF_10_MIN', 'DIFF10_DATE', 'DF_10_TD'
    # 以下字段已注释，不再需要
    # 'ma100', 'atr', 'atrma5','26D_DIFF1', '26D_DIFF2',
    # 'slope', 'slope-1', 'power_slope', 'r2_slope', 'slope90',
    # 'trix_26', 'trix_26与上一周差值', 'trix_66', 'trix_66与上一周差值',
    # 'EMA(13-26)的66周0阶平滑'
]

@contextmanager
def get_db_connection(db_url):
    """创建数据库连接的上下文管理器"""
    engine = create_engine(
        db_url,
        pool_size=5,          # 连接池大小
        max_overflow=10,      # 超过pool_size后最多可以创建的连接数
        pool_timeout=30,      # 连接池获取连接的超时时间
        pool_recycle=1800,    # 连接重置时间(30分钟)
        pool_pre_ping=True    # 连接前ping一下，确保连接有效
    )
    try:
        yield engine
    finally:
        engine.dispose()  # 确保连接被释放

def format_numeric_columns(df):
    """格式化数值列,统一保留6位小数"""
    numeric_columns = df.select_dtypes(include=['float64', 'float32']).columns
    if len(numeric_columns) > 0:
        df[numeric_columns] = df[numeric_columns].round(6)
    return df

def get_last_trade_week(date_str):
    """
    获取指定日期的上一个交易周的周五日期
    :param date_str: 日期字符串,格式为'YYYYMMDD'
    :return: 上一个交易周的周五日期字符串,格式为'YYYYMMDD'
    """
    date = datetime.strptime(date_str, '%Y%m%d')
    
    # 获取上周的周五日期
    last_week_friday = date - timedelta(days=7)
    last_friday = get_friday_of_current_week(last_week_friday)
    
    # 向前查找最多4个周五,寻找交易日
    for i in range(4):
        test_friday = last_friday - timedelta(days=i*7)
        test_date_str = test_friday.strftime('%Y-%m-%d')
        if is_trade_day(test_date_str):
            return test_friday.strftime('%Y%m%d')
    
    # 如果4个周五内没找到交易日,返回原日期(异常情况)
    return date_str

def get_latest_weekly_trade_date(engine):
    """
    从ts_stock_weekly表中获取最新的交易日期

    Parameters:
    -----------
    engine : sqlalchemy.Engine
        数据库引擎实例

    Returns:
    --------
    datetime.date
        最新的交易日期
    """
    try:
        query = "SELECT MAX(trade_date) FROM ts_stock_weekly"
        with engine.connect() as conn:
            result = conn.execute(text(query))
            latest_date = result.scalar()
            if latest_date:
                logger.info(f"从ts_stock_weekly表获取到的最新交易日期: {latest_date}")
                return latest_date
            else:
                log_warning(logger, "未从ts_stock_weekly表获取到交易日期，将使用交易日历")
                return None
    except Exception as e:
        log_error(logger, f"获取ts_stock_weekly最新日期时出错: {str(e)}")
        return None

def export_latest_weekly_macd_data(force_date=None):
    """从macd_analy_weekly_his表中导出数据到Excel
    :param force_date: 可选参数,指定强制使用的日期格式YYYY-MM-DD
    :return: bool 导出是否成功
    """
    # 数据库连接URL
    DB_URL = "mysql+pymysql://root:31490600@10.10.10.2:3306/instockdb"
    
    # 设置基础导出目录
    local_dir = '/Users/<USER>/Documents/git/stock-analysis-engine/股票财务指标数据'
    server_dir = '/home/<USER>/jupyter/stock_data'
    
    # 首先检查服务器目录是否存在
    if os.path.exists(server_dir):
        base_export_dir = server_dir
        logger.info(f"使用服务器导出路径: {server_dir}")
    # 如果服务器目录不存在，检查本地目录
    elif os.path.exists(local_dir):
        base_export_dir = local_dir
        logger.info(f"使用本地导出路径: {local_dir}")
    else:
        # 如果两个目录都不存在，创建本地目录并使用它
        os.makedirs(local_dir, exist_ok=True)
        base_export_dir = local_dir
        logger.info(f"创建并使用本地导出路径: {local_dir}")
    try:
        with get_db_connection(DB_URL) as engine:
            # 获取当前日期
            current_date = datetime.now().date()
            
            # 如果强制指定了日期，则使用该日期
            if force_date:
                try:
                    calc_date = datetime.strptime(force_date, '%Y-%m-%d').date()
                    logger.info(f"使用指定日期: {calc_date}")
                except ValueError:
                    log_error(logger, f"日期格式错误: {force_date}, 应为YYYY-MM-DD格式")
                    return False
            else:
                # 尝试从ts_stock_weekly表中获取最新交易日期（复用engine连接）
                latest_db_date = get_latest_weekly_trade_date(engine)

                if latest_db_date:
                    calc_date = latest_db_date
                    logger.info(f"使用ts_stock_weekly表中的最新日期: {calc_date}")
                else:
                    # 如果未获取到数据库日期，则使用交易日历逻辑
                    logger.info("未获取到数据库日期，将使用交易日历逻辑")
                    
                    # 获取本周五的日期
                    friday_date = get_friday_of_current_week(current_date)
                    
                    # 检查本周五是否为交易日
                    if is_trade_day(friday_date.strftime('%Y-%m-%d')):
                        calc_date = friday_date
                        logger.info(f"将使用本周五 {calc_date} 的数据")
                    else:
                        # 获取最近的交易日
                        calc_date = get_latest_trade_day(current_date)
                        logger.info(f"本周五不是交易日，将使用最近交易日: {calc_date}")
            
            # 转换为YYYYMMDD格式用于数据库查询
            query_date_str = calc_date.strftime('%Y%m%d')
            logger.info(f"查询日期为: {query_date_str}")
                
            # 从历史表获取数据
            query = text("""
                SELECT * FROM macd_analy_weekly_his 
                WHERE date = :date
            """)
            
            # 执行查询
            with engine.connect() as conn:
                df = pd.read_sql(query, conn, params={'date': query_date_str})
            
            if not df.empty:
                # 格式化数值列
                df = format_numeric_columns(df)
                
                # 重命名列
                df = df.rename(columns=COLUMN_MAPPING)
                
                # 重排列顺序
                available_columns = [col for col in COLUMN_ORDER if col in df.columns]
                df = df[available_columns]
                
                # 使用当前系统日期构建文件名
                current_date_str = datetime.now().strftime('%Y%m%d')
                # 从当前日期中提取年月信息并创建子目录
                year_month = current_date_str[:6]
                export_dir = os.path.join(base_export_dir, year_month)
                
                # 确保导出目录存在
                os.makedirs(export_dir, exist_ok=True)
                
                output_file = os.path.join(export_dir, f'macd_weekly_data_{current_date_str}.xlsx')
                
                # 检查文件是否已存在
                if os.path.exists(output_file):
                    logger.info(f"文件 {output_file} 已存在,跳过导出")
                    return True

                # 导出到Excel，添加错误处理
                try:
                    df.to_excel(output_file, index=False, sheet_name='周线数据', engine='openpyxl')
                    log_progress(logger, f"成功导出 {len(df)} 条周线记录到文件 {output_file}")

                    # 验证文件是否成功创建
                    if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                        logger.info(f"文件验证成功，大小: {os.path.getsize(output_file)} 字节")
                        return True
                    else:
                        log_error(logger, f"文件创建失败或文件为空: {output_file}")
                        return False
                except Exception as excel_error:
                    log_error(logger, f"Excel导出失败: {str(excel_error)}")
                    return False
            else:
                log_warning(logger, f"未找到日期 {query_date_str} 的周线数据")
                return False
                
    except Exception as e:
        log_error(logger, f"导出周线数据失败: {str(e)}")
        return False

if __name__ == '__main__':
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='导出股票周线MACD数据')
    parser.add_argument('--date', type=str, help='指定导出日期,格式为YYYY-MM-DD')
    args = parser.parse_args()
    
    # 执行导出操作
    try:
        result = export_latest_weekly_macd_data(force_date=args.date)
        if result:
            # 移除成功通知，只记录日志
            logger.info("周线MACD数据导出成功，数据导出任务已完成")
            # send_notification(
            #     "周线MACD数据导出成功",
            #     "周线数据导出任务已成功完成",
            #     "周线MACD数据|导出成功"
            # )
        else:
            send_notification(
                message="导出过程中发生错误,请检查日志获取详细信息",
                title="周线MACD数据导出失败",
                tags="周线MACD数据|导出失败"
            )
    except Exception as e:
        error_msg = str(e)
        log_error(logger, f"处理失败: {error_msg}")
        send_notification(
            message=f"导出过程中发生异常:{error_msg}",
            title="周线MACD数据导出异常",
            tags="周线MACD数据|系统异常"
        ) 