import numpy as np
import pandas as pd
import talib as ta
from scipy import stats
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
import time
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import queue
import threading
import os
import sys
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification
from trade_calendar import is_trade_day, get_latest_trade_day, get_friday_of_current_week
import argparse
from scipy.signal import savgol_filter
from MyTT import EMA  # 导入MyTT库中的EMA函数

def read_stock_data_from_db(db_url, stock_code, start_date=None, end_date=None):
    """
    从数据库中读取指定股票代码和日期区间的周线数据
    :param db_url: 数据库连接URL,例如 'mysql+pymysql://user:password@host:port/database'
    :param stock_code: 股票代码,如'000001'
    :param start_date: 开始日期,格式为'YYYY-MM-DD',可选
    :param end_date: 结束日期,格式为'YYYY-MM-DD',可选
    :return: 返回查询结果,如果没有找到返回None
    """
    engine = None
    try:
        # 创建数据库引擎
        engine = create_engine(db_url, pool_recycle=3600)
        
        # 构建查询语句 - 改为使用周线表
        query = f"""
            SELECT trade_date,stock_code,name,close,open,high,low,vol as volume 
            FROM ts_stock_weekly 
            WHERE stock_code = '{stock_code}'
        """
        
        # 添加日期区间条件
        if start_date and end_date:
            query += f" AND trade_date BETWEEN '{start_date}' AND '{end_date}'"
        elif start_date:
            query += f" AND trade_date >= '{start_date}'"
        elif end_date:
            query += f" AND trade_date <= '{end_date}'"
        
        # 按日期排序
        query += " ORDER BY trade_date DESC"
        
        # 执行查询
        with engine.connect() as conn:
            df = pd.read_sql(query, conn)
        
        # 返回查询结果
        if not df.empty:
            return df
        else:
            print(f"未找到股票 {stock_code} 在指定日期区间的周线数据")
            return None
    except Exception as e:
        print(f"从数据库读取周线数据失败: {str(e)}")
        return None
    finally:
        # 确保引擎被释放
        if engine:
            engine.dispose()

def check_latest_data(db_url):
    """
    检查数据库中的周线数据是否存在最新数据缺失的情况
    
    :param db_url: 数据库连接URL
    :return: 返回一个元组 (缺少最新数据的股票列表, 最新日期)
    """
    engine = None
    try:
        engine = create_engine(db_url, pool_recycle=3600)
        
        # 获取最新的交易日期
        latest_date_query = "SELECT MAX(trade_date) as latest_date FROM ts_stock_weekly"
        with engine.connect() as conn:
            latest_date_df = pd.read_sql(latest_date_query, conn)
            latest_date = latest_date_df['latest_date'].iloc[0]
        
        if latest_date:
            # 查询上次最新日期的记录数
            latest_count_query = f"SELECT COUNT(DISTINCT stock_code) as stock_count FROM ts_stock_weekly WHERE trade_date = '{latest_date}'"
            
            # 查询上次前一交易日的记录数
            # 找到上一个交易日
            prev_date_query = f"""
                SELECT MAX(trade_date) as prev_date 
                FROM ts_stock_weekly 
                WHERE trade_date < '{latest_date}'
            """
            
            with engine.connect() as conn:
                latest_count_df = pd.read_sql(latest_count_query, conn)
                prev_date_df = pd.read_sql(prev_date_query, conn)
                
                latest_count = latest_count_df['stock_count'].iloc[0]
                
                if not prev_date_df.empty and not pd.isna(prev_date_df['prev_date'].iloc[0]):
                    prev_date = prev_date_df['prev_date'].iloc[0]
                    
                    # 获取前一天的记录数
                    prev_count_query = f"SELECT COUNT(DISTINCT stock_code) as stock_count FROM ts_stock_weekly WHERE trade_date = '{prev_date}'"
                    prev_count_df = pd.read_sql(prev_count_query, conn)
                    prev_count = prev_count_df['stock_count'].iloc[0]
                    
                    # 如果最新日期的股票数量明显少于前一天，可能有部分数据缺失
                    if latest_count < prev_count * 0.95:  # 如果少于前一天的95%
                        log_warning(logger, f"最新日期 {latest_date} 的股票数量 ({latest_count}) 明显少于前一天 {prev_date} 的数量 ({prev_count})，可能有数据缺失")
                        
                        # 查找哪些股票在前一天有数据但在最新日期没有数据
                        missing_stocks_query = f"""
                            SELECT a.stock_code, a.name
                            FROM ts_stock_weekly a
                            WHERE a.trade_date = '{prev_date}'
                            AND NOT EXISTS (
                                SELECT 1 FROM ts_stock_weekly b
                                WHERE b.trade_date = '{latest_date}'
                                AND b.stock_code = a.stock_code
                            )
                        """
                        
                        missing_stocks_df = pd.read_sql(missing_stocks_query, conn)
                        
                        if not missing_stocks_df.empty:
                            return missing_stocks_df, latest_date
        
        return pd.DataFrame(), latest_date
        
    except Exception as e:
        log_error(logger, f"检查最新数据时出错: {str(e)}")
        return pd.DataFrame(), None
    finally:
        if engine:
            engine.dispose()

def get_stock_data(stock_code, start_date=None, end_date=None):
    """
    获取股票周线数据的封装函数(DB_URL 内置)
    :param stock_code: 股票代码
    :param start_date: 开始日期(可选)
    :param end_date: 结束日期(可选)
    :return: 返回股票数据DataFrame
    """
    # 内置数据库连接URL
    DB_URL = "mysql+pymysql://root:31490600@**********:3306/instockdb"
    
    # 调用 read_stock_data_from_db 函数
    result = read_stock_data_from_db(DB_URL, stock_code, start_date, end_date)
    if result is not None:
        return result
    else:
        return pd.DataFrame()  # 返回空DataFrame

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志
logger = setup_logger(script_name)

# def is_not_st(name):
#     """检查是否为非ST股票"""
#     return not str(name).upper().startswith(('*ST', 'ST'))

def is_valid_price(price):
    """检查价格是否有效"""
    if isinstance(price, str):
        return price != '-'
    return not pd.isna(price) and float(price) > 0

def calculate_slope(series, period):
    """Calculate linear regression slope for a given series and period"""
    return pd.Series(series).rolling(period).apply(
        lambda x: np.polyfit(range(len(x)), x, deg=1)[0] if len(x) == period else np.nan, 
        raw=True
    ).values

def get_all_stock_codes(db_url):
    """
    Get all stock codes from the database
    
    Parameters:
    -----------
    db_url : str
        Database connection URL
        
    Returns:
    --------
    list
        List of stock codes
    """
    try:
        engine = create_engine(db_url)
        query = "SELECT DISTINCT stock_code FROM stock_basic ORDER BY stock_code"
        df = pd.read_sql(query, engine)
        return df['stock_code'].tolist()
    except Exception as e:
        print(f"Error getting stock codes: {str(e)}")
        return []

def get_valid_stock_codes(db_url):
    """
    获取有效的股票代码，过滤掉ST股票和价格无效的股票
    
    Parameters:
    -----------
    db_url : str
        数据库连接URL
        
    Returns:
    --------
    list
        有效股票代码列表
    """
    try:
        engine = create_engine(db_url)
        
        # 查询股票，且最新价格有效的股票，并且周线数据数量大于等于30条
        # 注意：SQL中的%需要用%%转义，避免被误解为Python字符串格式化占位符
        # 已注释ST股票过滤：WHERE b.name NOT LIKE '%%ST%%' AND b.name NOT LIKE '*%%'
        query = """
            SELECT
                b.stock_code,
                b.name,
                d.close,
                COUNT(w.trade_date) as total_count,
                SUM(CASE WHEN w.close > 0 AND w.close IS NOT NULL THEN 1 ELSE 0 END) as valid_count,
                MAX(w.close) as max_price,
                MIN(w.close) as min_price,
                AVG(w.close) as avg_price,
                (MAX(w.close) - MIN(w.close)) / AVG(w.close) as price_variation_ratio
            FROM stock_basic b
            JOIN (
                SELECT stock_code, close
                FROM ts_stock_weekly
                WHERE trade_date = (SELECT MAX(trade_date) FROM ts_stock_weekly)
            ) d ON b.stock_code = d.stock_code
            JOIN ts_stock_weekly w ON b.stock_code = w.stock_code
            WHERE d.close > 0 AND d.close IS NOT NULL
            GROUP BY b.stock_code, b.name, d.close
            HAVING 
                COUNT(w.trade_date) >= 35
                AND SUM(CASE WHEN w.close > 0 AND w.close IS NOT NULL THEN 1 ELSE 0 END) >= 35
                AND (MAX(w.close) - MIN(w.close)) / AVG(w.close) >= 0.01
        """
        
        df = pd.read_sql(query, engine)
        
        if df.empty:
            logger.warning("未找到有效股票")
            return []
        
        # 打印数据统计
        total_stocks = len(df)
        avg_total_count = df['total_count'].mean()
        min_total_count = df['total_count'].min()
        avg_valid_count = df['valid_count'].mean()
        min_valid_count = df['valid_count'].min()
        avg_price_variation = df['price_variation_ratio'].mean()
        min_price_variation = df['price_variation_ratio'].min()
        
        logger.info(f"找到 {total_stocks} 只有效股票 (非ST、价格有效、有效数据点≥35、价格波动≥1%%)")
        logger.info(f"数据统计: 总数据量: 平均{avg_total_count:.1f}条, 最少{min_total_count}条")
        logger.info(f"有效数据量: 平均{avg_valid_count:.1f}条, 最少{min_valid_count}条")
        logger.info(f"价格波动率: 平均{avg_price_variation:.2%}, 最小{min_price_variation:.2%}")
        
        # 查看被过滤掉的股票数量
        # 1. 查询股票总数（已注释ST过滤）
        # total_non_st_query = "SELECT COUNT(DISTINCT stock_code) as count FROM stock_basic WHERE name NOT LIKE '%%ST%%' AND name NOT LIKE '*%%'"
        total_non_st_query = "SELECT COUNT(DISTINCT stock_code) as count FROM stock_basic"
        
        # 2. 查询总数据量足够但有效数据点不足的股票数（已注释ST过滤）
        insufficient_valid_data_query = """
            SELECT COUNT(*) as count
            FROM (
                SELECT
                    b.stock_code,
                    COUNT(w.trade_date) as total_count,
                    SUM(CASE WHEN w.close > 0 AND w.close IS NOT NULL THEN 1 ELSE 0 END) as valid_count
                FROM stock_basic b
                JOIN ts_stock_weekly w ON b.stock_code = w.stock_code
                GROUP BY b.stock_code
                HAVING
                    COUNT(w.trade_date) >= 35
                    AND SUM(CASE WHEN w.close > 0 AND w.close IS NOT NULL THEN 1 ELSE 0 END) < 35
            ) as tmp
        """
        
        # 3. 查询数据量不足35条的股票数
        insufficient_data_query = """
            SELECT COUNT(*) as count
            FROM (
                SELECT 
                    b.stock_code, 
                    COUNT(w.trade_date) as data_count
                FROM stock_basic b
                LEFT JOIN ts_stock_weekly w ON b.stock_code = w.stock_code
                WHERE b.name NOT LIKE '%%ST%%' AND b.name NOT LIKE '*%%'
                GROUP BY b.stock_code
                HAVING COUNT(w.trade_date) < 35 OR COUNT(w.trade_date) IS NULL
            ) as tmp
        """
        
        # 4. 查询数据量足够但波动不足的股票数
        insufficient_variation_query = """
            SELECT COUNT(*) as count
            FROM (
                SELECT 
                    b.stock_code, 
                    (MAX(w.close) - MIN(w.close)) / AVG(w.close) as price_variation_ratio,
                    COUNT(w.trade_date) as total_count,
                    SUM(CASE WHEN w.close > 0 AND w.close IS NOT NULL THEN 1 ELSE 0 END) as valid_count
                FROM stock_basic b
                JOIN ts_stock_weekly w ON b.stock_code = w.stock_code
                WHERE b.name NOT LIKE '%%ST%%' AND b.name NOT LIKE '*%%'
                GROUP BY b.stock_code
                HAVING 
                    COUNT(w.trade_date) >= 35
                    AND SUM(CASE WHEN w.close > 0 AND w.close IS NOT NULL THEN 1 ELSE 0 END) >= 35
                    AND (MAX(w.close) - MIN(w.close)) / AVG(w.close) < 0.01
            ) as tmp
        """
        
        with engine.connect() as conn:
            # 获取非ST股票总数
            total_result = conn.execute(text(total_non_st_query))
            total_non_st_stocks = total_result.scalar() or 0
            
            # 获取总数据量足够但有效数据点不足的股票数
            valid_data_result = conn.execute(text(insufficient_valid_data_query))
            insufficient_valid_data_stocks = valid_data_result.scalar() or 0
            
            # 获取数据量不足的股票数
            data_result = conn.execute(text(insufficient_data_query))
            insufficient_data_stocks = data_result.scalar() or 0
            
            # 获取数据量足够但波动不足的股票数
            variation_result = conn.execute(text(insufficient_variation_query))
            insufficient_variation_stocks = variation_result.scalar() or 0
            
            # 计算有效价格股票数
            valid_price_stocks = total_non_st_stocks - insufficient_data_stocks - insufficient_valid_data_stocks
            filtered_by_price = valid_price_stocks - total_stocks - insufficient_variation_stocks
            
            if filtered_by_price < 0:
                filtered_by_price = 0  # 确保不会出现负数
            
            logger.info(f"过滤统计:")
            logger.info(f"- 总股票数: {total_non_st_stocks} 只")  # 已注释ST过滤
            logger.info(f"- 因总数据量不足35条被过滤: {insufficient_data_stocks} 只 ({insufficient_data_stocks/total_non_st_stocks*100:.1f}%%)")
            logger.info(f"- 因有效数据点不足35个被过滤: {insufficient_valid_data_stocks} 只 ({insufficient_valid_data_stocks/total_non_st_stocks*100:.1f}%%)")
            logger.info(f"- 因价格波动不足被过滤: {insufficient_variation_stocks} 只 ({insufficient_variation_stocks/total_non_st_stocks*100:.1f}%%)")
            logger.info(f"- 因其他原因被过滤(无效价格等): {filtered_by_price} 只 ({filtered_by_price/total_non_st_stocks*100:.1f}%%)")
        
        return df['stock_code'].tolist()
    except Exception as e:
        logger.error(f"获取有效股票代码时出错: {str(e)}")
        # 打印更详细的错误信息
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return []

def batch_save_indicators_to_db(data_queue, db_url, batch_size=1000):
    """Batch save weekly indicators to database from a queue"""
    engine = create_engine(
        db_url,
        pool_size=10,          # 连接池大小
        max_overflow=20,       # 超过pool_size后最多可以创建的连接数
        pool_timeout=60,       # 连接池获取连接的超时时间
        pool_recycle=1800,     # 连接重置时间（30分钟）
        pool_pre_ping=True     # 连接前ping一下，确保连接有效
    )
    all_data = []  # 存储所有数据
    total_saved = 0
    
    try:
        while True:
            try:
                df = data_queue.get(timeout=60)
                if df is None:
                    break
                
                df = df.rename(columns={'trade_date': 'date', 'stock_code': 'code'})
                df['date'] = pd.to_datetime(df['date']).dt.date
                all_data.append(df)
                
            except queue.Empty:
                break
        
        if all_data:
            # 合并所有数据
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # 删除现有记录
            codes = combined_df['code'].unique()
            dates = combined_df['date'].unique()
            
            # 批量处理代码和日期，避免参数过多
            if len(codes) > 500:
                # 分批处理代码
                code_chunks = [codes[i:i+500] for i in range(0, len(codes), 500)]
                for chunk in code_chunks:
                    with engine.begin() as conn:
                        delete_query = text("""
                            DELETE FROM cn_stock_indicators_weekly 
                            WHERE code IN :codes 
                            AND date IN :dates
                        """)
                        conn.execute(delete_query, {"codes": tuple(chunk), "dates": tuple(dates)})
            else:
                with engine.begin() as conn:
                    delete_query = text("""
                        DELETE FROM cn_stock_indicators_weekly 
                        WHERE code IN :codes 
                        AND date IN :dates
                    """)
                    conn.execute(delete_query, {"codes": tuple(codes), "dates": tuple(dates)})
            
            # 一次性写入所有数据，增加每批次大小提高性能
            combined_df.to_sql('cn_stock_indicators_weekly', engine, 
                             if_exists='append', 
                             index=False,
                             method='multi',
                             chunksize=10000)  # 增加chunksize以提高写入性能
            
            total_saved = len(combined_df)
            logger.info(f"Saved all {total_saved} weekly indicator records to database")
                
    except Exception as e:
        logger.error(f"Error in batch save process: {str(e)}")
    finally:
        engine.dispose()  # 确保引擎被释放
        logger.info(f"Batch save process completed. Total saved: {total_saved}")

def calculate_indicators(stock_code, start_date=None, end_date=None):
    """Calculate technical indicators for weekly data of a given stock"""
    try:
        if start_date:
            start_date_dt = datetime.strptime(start_date, '%Y-%m-%d')
            # 对于周线数据，需要更长的历史数据来计算指标
            extended_start_date = (start_date_dt - timedelta(days=365*2)).strftime('%Y-%m-%d')
        else:
            extended_start_date = None
            
        df = get_stock_data(stock_code, extended_start_date, end_date)
        if df.empty:
            reason = "无周线数据"
            logger.warning(f"No weekly data returned for stock {stock_code}")
            return pd.DataFrame(), reason

        # ST股票检查（已注释）
        # if df['name'].iloc[0].upper().startswith(('*ST', 'ST')):
        #     reason = f"ST股票: {df['name'].iloc[0]}"
        #     logger.info(f"Skipping ST stock that passed initial filter: {stock_code} - {df['name'].iloc[0]}")
        #     return pd.DataFrame(), reason

        # 统一的数据质量检查
        if len(df) < 30:
            reason = f"周线数据不足: {len(df)}周，最低需要30周"
            logger.warning(f"Insufficient weekly data for stock {stock_code}: {len(df)} weeks, minimum required is 30 weeks")
            return pd.DataFrame(), reason
            
        # 检查无效价格的比例
        invalid_prices = df['close'].isnull().sum() + (df['close'] == 0).sum()
        if invalid_prices / len(df) > 0.1:  # 如果超过10%的价格数据无效
            reason = f"无效价格比例过高: {invalid_prices}/{len(df)} ({invalid_prices/len(df)*100:.1f}%)"
            logger.warning(f"Too many invalid prices for stock {stock_code}: {invalid_prices}/{len(df)} ({invalid_prices/len(df)*100:.1f}%)")
            return pd.DataFrame(), reason
        
        # 检查是否有足够的有效价格数据计算MACD
        # MACD需要至少26+9个数据点才能有一个完整的计算结果
        min_required_for_macd = 26 + 9  # fastperiod + signalperiod
        if len(df) - invalid_prices < min_required_for_macd:
            reason = f"有效数据点不足以计算MACD: {len(df) - invalid_prices}/{min_required_for_macd}"
            logger.warning(f"Not enough valid data points for MACD calculation: {len(df) - invalid_prices}/{min_required_for_macd} for stock {stock_code}")
            return pd.DataFrame(), reason
        
        # 检查价格变动是否足够 - 如果价格几乎不变动，MACD可能全为0
        price_range = df['close'].max() - df['close'].min()
        price_mean = df['close'].mean()
        if price_mean > 0 and price_range / price_mean < 0.01:  # 如果价格变动小于平均价格的1%
            reason = f"价格变动太小: 范围={price_range:.4f}, 平均={price_mean:.4f}, 比例={price_range/price_mean:.4f}"
            logger.warning(f"Price variation too small for stock {stock_code}: range={price_range:.4f}, mean={price_mean:.4f}, ratio={price_range/price_mean:.4f}")
            return pd.DataFrame(), reason
        
        # Ensure we have the required columns
        required_columns = ['trade_date', 'stock_code', 'name', 'open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            missing_cols = [col for col in required_columns if col not in df.columns]
            reason = f"缺少必要列: {', '.join(missing_cols)}"
            raise ValueError(f"Missing required columns in stock weekly data: {missing_cols}")
        
        # Create a copy to avoid modifying original data
        data = df.copy()
        
        # Convert trade_date to datetime if it's not already
        if not pd.api.types.is_datetime64_any_dtype(data['trade_date']):
            data['trade_date'] = pd.to_datetime(data['trade_date'])
        
        # Sort data by date in ascending order for proper calculation
        data = data.sort_values('trade_date', ascending=True)
        
        # Convert price columns to float type if they aren't already
        price_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in price_cols:
            data[col] = pd.to_numeric(data[col], errors='coerce')
        
        # 为周线数据调整参数
        # 日线参数除以5得到周线对应参数，再适当调整

        # 检查价格数据有效性
        invalid_prices = data['close'].isnull().sum()
        zero_prices = (data['close'] == 0).sum()
        if invalid_prices > 0 or zero_prices > 0:
            logger.warning(f"Stock {stock_code} has {invalid_prices} NaN prices and {zero_prices} zero prices")
        
        # 记录数据基本信息（用于错误日志）
        data_length = len(data)
        data_start = data['trade_date'].min()
        data_end = data['trade_date'].max()
        
        # Calculate MACD indicators - 周线参数调整
        try:
            # 先检查输入数据是否合适
            if data['close'].isnull().all() or (data['close'] == 0).all():
                raise ValueError(f"全部收盘价都是无效值，股票代码: {stock_code}")

            macd, macds, macdh = ta.MACD(data['close'].values,
                                        fastperiod=12,    # 使用与日线相同的标准参数
                                        slowperiod=26,    # 使用与日线相同的标准参数
                                        signalperiod=9)   # 使用与日线相同的标准参数

            # 简化的有效性检查：只在异常情况下记录详细信息
            if np.isnan(macd).all():
                logger.warning(f"Stock {stock_code}: All MACD values are NaN (data length: {len(data)})")

            data['macd'] = macd
            data['macds'] = macds
            data['diff_26d'] = data['macd'].diff()
            # macd_diff1 始终表示当期 macd
            data['macd_diff1'] = data['macd']
            # macd_diff2 将在后续步骤中通过shift(1)计算，这里暂不设置

            # 对于MACD全为NaN的情况，记录警告但继续处理
            if data['macd'].isna().all():
                logger.warning(f"Stock {stock_code}: All MACD values are NaN, but continuing to process")
        except Exception as e:
            logger.error(f"Error calculating MACD for stock {stock_code}: {str(e)}")
            logger.error(f"Data length: {data_length}, Date range: {data_start} to {data_end}")
            raise ValueError(f"MACD计算失败: {str(e)}")
        
        # Calculate MA indicators - 周线参数调整
        try:
            data['ma26'] = EMA(data['close'].values, 26)  # 使用标准26周期
            data['ma_26d'] = data['ma26'].diff()

            # 简化的有效性检查
            if np.isnan(data['ma26']).all():
                logger.warning(f"Stock {stock_code}: All MA26 values are NaN")
        except Exception as e:
            logger.error(f"Error calculating MA26 for stock {stock_code}: {str(e)}")
            raise ValueError(f"MA26计算失败: {str(e)}")
        
        # Fill NaN values with 0 for specific columns (保持MACD相关字段的完整性)
        fillna_columns = ['ma26', 'ma_26d', 'diff_26d']
        for col in fillna_columns:
            if col in data.columns:
                data[col] = data[col].fillna(0)
        
        # 计算 macd_diff2（数据已经在第508行按升序排序了）
        data['macd_diff2'] = data['macd'].shift(1).round(6)

        # Select and return only the requested columns
        columns = ['trade_date', 'stock_code', 'name', 'close', 'macd', 'macds', 'ma26', 'ma_26d', 'diff_26d', 'macd_diff1', 'macd_diff2']

        result = data[columns]

        # 在macd_diff2计算正确之后，进行智能过滤以减少写入数据库的数据量
        # 保留必要的历史数据用于验证macd_diff2的正确性
        if start_date:
            start_date_dt = pd.to_datetime(start_date)
            # 策略：保留前一周的数据用于验证，确保macd_diff2的正确性
            # 同时大幅减少返回的数据量，提高写入性能
            min_required_date = start_date_dt - pd.Timedelta(weeks=1)
            result = result[result['trade_date'] >= min_required_date]

            # 过滤信息仅在显著减少数据量时记录
            if len(result) < len(data) * 0.1:  # 只有当数据量减少90%以上时才记录
                logger.debug(f"智能过滤：从 {len(data)} 条减少到 {len(result)} 条数据")

        # 最后按降序排序返回，但保持已计算好的 macd_diff2 值
        result = result.sort_values('trade_date', ascending=False)
        return result, None  # 返回结果和None表示没有跳过原因
        
    except Exception as e:
        logger.error(f"Error calculating weekly indicators for {stock_code}: {str(e)}")
        return pd.DataFrame(), str(e)

def process_stock(stock_code, start_date, end_date):
    """Process a single stock and return its weekly indicators"""
    try:
        logger.debug(f"Processing stock weekly data: {stock_code}")
        indicators_df, skip_reason = calculate_indicators(stock_code, start_date, end_date)
        return indicators_df, skip_reason
    except Exception as e:
        logger.error(f"Error processing stock weekly data {stock_code}: {str(e)}")
        return pd.DataFrame(), str(e)

def process_all_stocks(start_date=None, end_date=None, db_url=None, max_workers=None):
    """Process all stocks using parallel processing for weekly indicators"""
    if db_url is None:
        db_url = "mysql+pymysql://root:31490600@**********:3306/instockdb"
    
    if max_workers is None:
        # 限制最大工作进程数，避免创建过多数据库连接
        cpu_count = mp.cpu_count()
        # 使用CPU核心数的一半，至少为2，最多为8
        max_workers = max(2, min(8, cpu_count // 2))
        logger.info(f"Using {max_workers} workers (CPU count: {cpu_count})")
    
    start_time = time.time()
    error_stocks = []  # 记录处理失败的股票
    processed_stocks = []  # 记录处理成功的股票
    skipped_stocks = []  # 记录被跳过的股票(ST股票或无效价格)
    skip_reasons = {}  # 记录每只被跳过股票的具体原因
    
    try:
        # 改为获取有效的股票代码，过滤掉ST股票和价格无效的股票
        stock_codes = get_valid_stock_codes(db_url)
        if not stock_codes:
            error_msg = "No valid stock codes found in database"
            log_error(logger, error_msg)
            send_notification(
                message=f"错误信息:{error_msg}\n时间:{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                title="股票周线指标计算失败", 
                tags="周线指标|失败")
            return
        
        logger.info(f"Found {len(stock_codes)} valid stocks to process weekly indicators using {max_workers} workers")
        # 新增：如果有效股票数量少于5000，发送告警
        if len(stock_codes) < 5000:
            send_notification(
                message=(
                    f"警告：本次可用有效股票数量仅为 {len(stock_codes)} 只，低于5000只，可能存在数据缺失或过滤条件过严。\n"
                    f"请检查数据源或过滤逻辑。\n"
                    f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                ),
                title="有效股票数量异常告警",
                tags="周线指标|股票数量异常"
            )
        
        # 验证日期格式
        try:
            if start_date:
                datetime.strptime(start_date, '%Y-%m-%d')
            if end_date:
                datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError as e:
            error_msg = f"Invalid date format: {str(e)}"
            log_error(logger, error_msg)
            send_notification(
                message=f"错误信息:{error_msg}",
                title="股票周线指标计算失败",
                tags="周线指标|失败"
            )
            return
        
        data_queue = mp.Manager().Queue()
        writer_thread = threading.Thread(
            target=batch_save_indicators_to_db,
            args=(data_queue, db_url),
            daemon=True
        )
        writer_thread.start()
        
        total_processed = 0
        successful_processed = 0
        
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            future_to_stock = {
                executor.submit(process_stock, code, start_date, end_date): code 
                for code in stock_codes
            }
            
            for future in as_completed(future_to_stock):
                stock_code = future_to_stock[future]
                try:
                    indicators_df, skip_reason = future.result()
                    
                    if indicators_df is None:  # 处理失败
                        error_stocks.append((stock_code, "处理返回None"))
                    elif indicators_df.empty:  # 空数据(可能是ST股票或无效价格)
                        skipped_stocks.append(stock_code)
                        if skip_reason:  # 记录跳过原因
                            skip_reasons[stock_code] = skip_reason
                    else:  # 成功处理
                        # 验证计算结果
                        if indicators_df['macd'].isna().all():
                            error_reason = "周线MACD计算结果全为NaN，可能是数据不足或数据质量问题"
                            error_stocks.append((stock_code, error_reason))
                            skip_reasons[stock_code] = error_reason
                        elif (indicators_df['macd'] == 0).all():
                            error_reason = "周线MACD计算结果全为0，可能是股票价格波动极小"
                            error_stocks.append((stock_code, error_reason))
                            skip_reasons[stock_code] = error_reason
                        elif indicators_df['ma26'].isna().all():
                            error_reason = "周线MA26计算结果全为NaN"
                            error_stocks.append((stock_code, error_reason))
                            skip_reasons[stock_code] = error_reason
                        elif (indicators_df['ma26'] == 0).all():
                            error_reason = "周线MA26计算结果全为0"
                            error_stocks.append((stock_code, error_reason))
                            skip_reasons[stock_code] = error_reason
                        else:
                            data_queue.put(indicators_df)
                            successful_processed += 1
                            processed_stocks.append(stock_code)
                    
                    total_processed += 1
                    
                    if total_processed % 100 == 0:
                        elapsed_time = time.time() - start_time
                        avg_time_per_stock = elapsed_time / total_processed
                        remaining_stocks = len(stock_codes) - total_processed
                        estimated_remaining_time = remaining_stocks * avg_time_per_stock
                        
                        logger.info(
                            f"Progress: {total_processed}/{len(stock_codes)} stocks (weekly) "
                            f"({total_processed/len(stock_codes)*100:.1f}%) "
                            f"[{elapsed_time/60:.1f}min elapsed, "
                            f"{estimated_remaining_time/60:.1f}min remaining]\n"
                            f"Successful: {successful_processed}, "
                            f"Skipped: {len(skipped_stocks)}, "
                            f"Failed: {len(error_stocks)}"
                        )
                    
                except Exception as e:
                    error_msg = f"Error processing stock weekly data {stock_code}: {str(e)}"
                    logger.error(error_msg)
                    error_stocks.append((stock_code, str(e)))
        
        # 等待所有数据写入完成
        data_queue.put(None)
        writer_thread.join(timeout=300)  # 等待最多5分钟
        
        total_time = time.time() - start_time
        completion_msg = (
            f"\nWeekly indicators processing completed!\n"
            f"Total stocks processed: {total_processed}\n"
            f"Successfully processed: {successful_processed}\n"
            f"Skipped stocks: {len(skipped_stocks)}\n"
            f"Failed stocks: {len(error_stocks)}\n"
            f"Total time taken: {total_time/60:.2f} minutes\n"
            f"Average time per stock: {total_time/total_processed:.2f} seconds"
        )
        logger.info(completion_msg)
        
        # 显示失败的股票及原因
        if len(error_stocks) > 0:
            error_details = "\nFailed stocks with reasons:\n"
            for stock_code, error_reason in error_stocks:
                error_details += f"- {stock_code}: {error_reason}\n"
            
            logger.error(error_details)
        
        # 汇总跳过原因统计
        if skip_reasons:
            reason_counts = {}
            for reason in skip_reasons.values():
                reason_counts[reason] = reason_counts.get(reason, 0) + 1
            
            # 按照频率排序
            sorted_reasons = sorted(reason_counts.items(), key=lambda x: x[1], reverse=True)
            
            skip_summary = "\nSkipped stocks reason summary:\n"
            for reason, count in sorted_reasons:
                skip_summary += f"- {reason}: {count} stocks\n"
            
            logger.info(skip_summary)
            
            # 展示部分每种原因的具体股票
            skip_details = "\nSelected skipped stocks by reason:\n"
            for reason, _ in sorted_reasons:
                # 找出最多5个该原因的股票
                stocks_with_reason = [code for code, r in skip_reasons.items() if r == reason][:5]
                if stocks_with_reason:
                    skip_details += f"\n{reason}:\n"
                    for code in stocks_with_reason:
                        skip_details += f"- {code}\n"
            
            logger.info(skip_details)
        
        # 发送处理结果通知
        if len(error_stocks) > 0 or len(skipped_stocks) > 0:
            # 发送处理结果通知
            desp = (
                f"处理时间:{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"总处理股票数:{total_processed}\n"
                f"成功处理数:{successful_processed}\n"
                f"跳过股票数:{len(skipped_stocks)}\n"
                f"失败数量:{len(error_stocks)}\n"
                f"总耗时:{total_time/60:.2f}分钟\n"
                f"平均每只股票耗时:{total_time/total_processed:.2f}秒\n\n"
            )
            
            # 添加跳过原因统计
            if skip_reasons:
                desp += "\n跳过原因统计:\n"
                for reason, count in sorted_reasons:
                    desp += f"- {reason}: {count} 只股票\n"
            
            if skipped_stocks:
                desp += f"\n跳过的股票示例(前10个):\n"
                for code in skipped_stocks[:10]:
                    reason = skip_reasons.get(code, "未知原因")
                    desp += f"- {code}: {reason}\n"
                if len(skipped_stocks) > 10:
                    desp += f"... 还有 {len(skipped_stocks) - 10} 个股票被跳过\n"
            
            if error_stocks:
                desp += "\n失败股票列表(全部):\n"
                for stock_code, error in error_stocks:
                    desp += f"- {stock_code}: {error}\n"
            
            title = "股票周线指标计算"
            if len(error_stocks) > 0:
                title += "出现错误"
                tags = "周线指标|错误"
            else:
                title += "完成（存在跳过的股票）"
                tags = "周线指标|完成"
            
            send_notification(
                message=desp,
                title=title, 
                tags=tags
            )
        
    except Exception as e:
        error_msg = f"Critical error in process_all_stocks (weekly): {str(e)}"
        log_error(logger, error_msg)
        send_notification(
            message=f"错误信息:{error_msg}\n时间:{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            title="股票周线指标计算发生严重错误",
            tags="周线指标|严重错误"
        )

def get_friday_of_week(date):
    """
    Get the Friday of the week containing the specified date
    
    Parameters:
    -----------
    date : datetime.date
        The date to find the corresponding Friday for
        
    Returns:
    --------
    datetime.date
        The Friday of the same week
    """
    # Calculate days until Friday (weekday 4 = Friday)
    days_to_friday = 4 - date.weekday()
    if days_to_friday < 0:  # If today is Saturday/Sunday, get next Friday
        days_to_friday += 7
    
    friday = date + timedelta(days=days_to_friday)
    return friday

class MacdWeeklyDivergenceAnalyzer:
    def __init__(self, db_url="mysql+pymysql://root:31490600@**********:3306/instockdb"):
        self.db_url = db_url
        self.engine = create_engine(
            db_url,
            pool_size=5,         # 连接池大小
            max_overflow=10,     # 超过pool_size后最多可以创建的连接数
            pool_timeout=30,     # 连接池获取连接的超时时间
            pool_recycle=1800,   # 连接重置时间（30分钟）
            pool_pre_ping=True   # 连接前ping一下，确保连接有效
        )

def get_latest_weekly_trade_date(db_url="mysql+pymysql://root:31490600@**********:3306/instockdb"):
    """
    从ts_stock_weekly表中获取最新的交易日期
    
    Parameters:
    -----------
    db_url : str
        数据库连接URL
        
    Returns:
    --------
    datetime.date
        最新的交易日期
    """
    try:
        engine = create_engine(db_url)
        query = "SELECT MAX(trade_date) FROM ts_stock_weekly"
        with engine.connect() as conn:
            result = conn.execute(text(query))
            latest_date = result.scalar()
            if latest_date:
                log_progress(logger, f"从ts_stock_weekly表获取到的最新交易日期: {latest_date}")
                return latest_date
            else:
                log_warning(logger, "未从ts_stock_weekly表获取到交易日期，将使用交易日历")
                return None
    except Exception as e:
        log_error(logger, f"获取ts_stock_weekly最新日期时出错: {str(e)}")
        return None

if __name__ == "__main__":
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='计算股票周线技术指标')
    parser.add_argument('--force', action='store_true', help='强制使用最近交易日的数据进行计算')
    parser.add_argument('--date', type=str, help='指定计算日期,格式为YYYY-MM-DD')
    parser.add_argument('--start_date', type=str, help='指定计算开始日期,格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, help='指定计算结束日期,格式为YYYY-MM-DD')
    parser.add_argument('--check', action='store_true', help='只检查数据库中是否存在缺失最新日期数据的股票，不执行指标计算')
    args = parser.parse_args()
    
    # 内置数据库连接URL
    DB_URL = "mysql+pymysql://root:31490600@**********:3306/instockdb"
    
    # 如果只是检查数据库中是否有缺失最新日期的数据
    if args.check:
        log_progress(logger, "正在检查数据库中周线数据的完整性...")
        missing_stocks_df, latest_date = check_latest_data(DB_URL)
        
        if not missing_stocks_df.empty:
            # 记录存在数据缺失
            formatted_date = latest_date.strftime('%Y-%m-%d') if isinstance(latest_date, datetime) else str(latest_date)
            log_warning(logger, f"发现 {len(missing_stocks_df)} 只股票在最新日期 {formatted_date} 缺失数据")
            
            # 显示部分缺失的股票
            missing_count = len(missing_stocks_df)
            sample_size = min(50, missing_count)
            missing_samples = missing_stocks_df.head(sample_size)
            
            missing_desc = "\n".join([
                f"- {row['stock_code']} ({row['name']})"
                for _, row in missing_samples.iterrows()
            ])
            log_warning(logger, f"部分缺失最新数据的股票:\n{missing_desc}")
            
            # 发送告警通知
            send_notification(
                message=(
                    f"周线数据检查发现问题:\n"
                    f"共有 {missing_count} 只股票在最新日期 {formatted_date} 没有数据\n\n"
                    f"部分缺失数据的股票(前{sample_size}个):\n{missing_desc}\n\n"
                    f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                ),
                title="周线数据检查警告 - 部分股票缺失最新数据",
                tags="周线数据|警告"
            )
        else:
            if latest_date:
                formatted_date = latest_date.strftime('%Y-%m-%d') if isinstance(latest_date, datetime) else str(latest_date)
                log_progress(logger, f"数据库周线数据完整性检查通过，最新日期 {formatted_date} 的数据完整")
            else:
                log_warning(logger, "无法确定数据库中周线数据的最新日期")
        
        exit(0)
    
    # 默认先检查数据库中是否有缺失最新日期的数据
    log_progress(logger, "检查数据库中周线数据的完整性...")
    missing_stocks_df, latest_date = check_latest_data(DB_URL)
    
    if not missing_stocks_df.empty:
        # 记录存在数据缺失
        formatted_date = latest_date.strftime('%Y-%m-%d') if isinstance(latest_date, datetime) else str(latest_date)
        log_warning(logger, f"发现 {len(missing_stocks_df)} 只股票在最新日期 {formatted_date} 缺失数据")
        
        # 显示部分缺失的股票
        missing_count = len(missing_stocks_df)
        sample_size = min(20, missing_count)  # 减少默认显示的缺失股票数量
        missing_samples = missing_stocks_df.head(sample_size)
        
        missing_desc = "\n".join([
            f"- {row['stock_code']} ({row['name']})"
            for _, row in missing_samples.iterrows()
        ])
        log_warning(logger, f"部分缺失最新数据的股票(前{sample_size}个):\n{missing_desc}")
    else:
        if latest_date:
            formatted_date = latest_date.strftime('%Y-%m-%d') if isinstance(latest_date, datetime) else str(latest_date)
            log_progress(logger, f"数据库周线数据完整性检查通过，最新日期 {formatted_date} 的数据完整")
        else:
            log_warning(logger, "无法确定数据库中周线数据的最新日期")
    
    # 获取当天日期
    today = datetime.now().date()
    
    # 处理时间段计算
    if args.start_date and args.end_date:
        try:
            start_date_obj = datetime.strptime(args.start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(args.end_date, '%Y-%m-%d').date()
            
            # 转换为各自所在周的周五
            start_friday = get_friday_of_current_week(start_date_obj)
            end_friday = get_friday_of_current_week(end_date_obj)
            
            start_date = start_friday.strftime('%Y-%m-%d')
            end_date = end_friday.strftime('%Y-%m-%d')
            
            logger.info(f"将计算从 {start_date} (周五) 到 {end_date} (周五) 的股票周线指标数据")
            
            # 执行计算
            process_all_stocks(
                start_date=start_date,
                end_date=end_date,
                max_workers=mp.cpu_count()
            )
            exit(0)
        except ValueError:
            logger.error(f"日期格式错误: 应为YYYY-MM-DD格式")
            exit(1)
    # 添加处理只有start_date的情况
    elif args.start_date:
        try:
            start_date_obj = datetime.strptime(args.start_date, '%Y-%m-%d').date()
            
            # 转换为所在周的周五
            start_friday = get_friday_of_current_week(start_date_obj)
            start_date = start_friday.strftime('%Y-%m-%d')
            
            # 使用当前日期作为结束日期
            end_friday = get_friday_of_current_week(today)
            end_date = end_friday.strftime('%Y-%m-%d')
            
            logger.info(f"将计算从 {start_date} (周五) 到 {end_date} (周五) 的股票周线指标数据")
            
            # 执行计算
            process_all_stocks(
                start_date=start_date,
                end_date=end_date,
                max_workers=mp.cpu_count()
            )
            exit(0)
        except ValueError:
            logger.error(f"日期格式错误: 应为YYYY-MM-DD格式")
            exit(1)
    
    # 确定要使用的日期
    if args.date:
        try:
            calc_date = datetime.strptime(args.date, '%Y-%m-%d').date()
            log_progress(logger, f"使用指定日期: {calc_date}")
        except ValueError:
            log_error(logger, f"日期格式错误: {args.date},应为YYYY-MM-DD格式")
            exit(1)
    else:
        # 尝试从ts_stock_weekly表中获取最新交易日期
        latest_db_date = get_latest_weekly_trade_date()
        
        if latest_db_date and not args.force:
            calc_date = latest_db_date
            log_progress(logger, f"使用ts_stock_weekly表中的最新日期: {calc_date}")
            
        else:
            # 如果未获取到数据库日期或强制使用交易日历，则使用交易日历逻辑
            log_progress(logger, "未获取到数据库日期或强制使用交易日历，将使用交易日历逻辑")
            
            # 检查今天是否为交易日
            is_today_trade = is_trade_day(today)
            log_progress(logger, f"今天 {today} {'是' if is_today_trade else '不是'}交易日")
            
            # 获取本周五的日期
            friday_date = get_friday_of_current_week(today)
            log_progress(logger, f"本周五的日期是: {friday_date}")
            
            # 检查本周五是否为交易日
            is_friday_trade_day = is_trade_day(friday_date)
            log_progress(logger, f"本周五 {friday_date} {'是' if is_friday_trade_day else '不是'}交易日")
            
            # 获取最近的交易日
            latest_trade_day = get_latest_trade_day(today)
            log_progress(logger, f"最近的交易日是: {latest_trade_day}")
            
            # 如果本周五是交易日，使用本周五；否则使用最近的交易日
            if is_friday_trade_day and not args.force:
                calc_date = friday_date
                log_progress(logger, f"将使用本周五 {calc_date} 的数据")
            else:
                calc_date = latest_trade_day
                log_progress(logger, f"将使用最近交易日 {calc_date} 的数据进行计算")
    
    # 转换日期格式
    start_date = end_date = calc_date.strftime('%Y-%m-%d')
    log_progress(logger, f"开始计算 {start_date} 的股票周线指标数据")
    
    # 执行计算
    process_all_stocks(
        start_date=start_date,
        end_date=end_date,
        max_workers=mp.cpu_count()
    ) 