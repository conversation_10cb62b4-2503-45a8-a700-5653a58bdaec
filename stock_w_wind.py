#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WindPy API 周线数据获取脚本
用于获取各类高质量的金融周线数据

使用 w.wsd() 函数获取周线数据,通过设置 options 参数中的 Period=W 来指定周线数据
"""

from WindPy import w
import pandas as pd
from datetime import datetime, timedelta
import sys
import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
import time
import warnings
import pathlib
from contextlib import contextmanager
from sqlalchemy.pool import QueuePool


# 创建日志目录
log_dir = pathlib.Path(__file__).parent / 'log'
log_dir.mkdir(exist_ok=True)

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': 13306,
    'user': 'root',
    'password': '31490600',
    'database': 'instockdb',
    'charset': 'utf8mb4'
}

# 处理配置
PROCESS_CONFIG = {
    'show_progress': True,   # 是否显示进度信息
    'max_retries': 3,       # 最大重试次数
    'retry_delay': 5,       # 重试延迟(秒)
    'api_interval': 0.01,    # API调用间隔(秒)
    'check_connection': False,  # 是否每次检查API连接
    'connection_wait': 0.001,  # 连接等待时间(秒)
}

# 数据库连接池配置
DB_POOL_SIZE = 5
DB_MAX_OVERFLOW = 10
DB_POOL_TIMEOUT = 30

# 全局数据库引擎
_DB_ENGINE = None

# 简单的日志函数
def log_progress(message):
    """记录进度日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")
    with open(log_dir / f"{script_name}.log", 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] {message}\n")

def log_error(message):
    """记录错误日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] ERROR: {message}")
    with open(log_dir / f"{script_name}.log", 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] ERROR: {message}\n")


def start_wind_api(wait_time=120):
    """
    启动WindPy API
    
    参数:
        wait_time (int): 超时时间,默认120秒
        
    返回:
        bool: 启动是否成功
    """
    log_progress("正在启动WindPy API...")
    try:
        # 尝试导入WindPy
        from WindPy import w
        log_progress("WindPy模块导入成功")
        
        # 启动API
        result = w.start(waitTime=wait_time)
        log_progress(f"WindPy启动返回值: {result}")
        
        # WindPy返回值是一个对象,需要检查ErrorCode属性
        if hasattr(result, 'ErrorCode') and result.ErrorCode != 0:
            log_error("WindPy API启动失败")
            log_error("\n可能的解决方案:")
            log_error("1. 确保已安装Wind终端")
            log_error("2. 确保Wind终端正在运行")
            log_error("3. 确保已正确登录Wind终端")
            log_error("4. 确保已安装WindPy插件")
            log_error("5. 尝试增加wait_time参数值")
            return False
        
        if not w.isconnected():
            log_error("WindPy未连接成功")
            return False
            
        log_progress("WindPy API启动成功")
        return True
    except ImportError:
        log_error("错误: 无法导入WindPy模块")
        log_error("\n可能的解决方案:")
        log_error("1. 确保已安装WindPy插件")
        log_error("2. 检查Python环境变量设置")
        log_error("3. 尝试重新安装WindPy插件")
        return False
    except Exception as e:
        log_error(f"WindPy API启动失败,错误信息: {str(e)}")
        log_error("\n可能的解决方案:")
        log_error("1. 重启Wind终端")
        log_error("2. 检查网络连接")
        log_error("3. 联系Wind技术支持")
        return False

def stop_wind_api():
    """停止WindPy API"""
    log_progress("正在停止WindPy API...")
    w.stop()
    log_progress("WindPy API已停止")

def get_db_engine(config=None):
    """
    获取SQLAlchemy数据库引擎(使用连接池)
    
    参数:
        config (dict): 数据库配置,如果为None则使用默认配置
        
    返回:
        Engine: SQLAlchemy引擎对象
    """
    global _DB_ENGINE
    
    if config is None:
        # 使用全局数据库配置
        config = DB_CONFIG.copy()
    
    # 如果已存在引擎且状态正常,直接返回
    if _DB_ENGINE is not None:
        try:
            # 测试连接
            with _DB_ENGINE.connect() as conn:
                conn.execute(text("SELECT 1"))
            return _DB_ENGINE
        except Exception:
            # 连接失败,需要重新创建
            _DB_ENGINE = None
    
    # 创建新的数据库引擎
    try:
        from sqlalchemy.engine.url import URL
        db_url = URL.create(
            drivername="mysql+pymysql",
            username=config['user'],
            password=config['password'],
            host=config['host'],
            port=config['port'],
            database=config['database']
        )
        
        _DB_ENGINE = create_engine(
            db_url,
            poolclass=QueuePool,
            pool_size=DB_POOL_SIZE,
            max_overflow=DB_MAX_OVERFLOW,
            pool_timeout=DB_POOL_TIMEOUT,
            pool_recycle=3600  # 1小时后回收连接
        )
        
        # 测试连接
        with _DB_ENGINE.connect() as conn:
            conn.execute(text("SELECT 1"))
            
        log_progress("MySQL数据库引擎创建成功(使用连接池)")
        return _DB_ENGINE
        
    except Exception as e:
        log_error(f"创建数据库引擎失败: {str(e)}")
        return None

@contextmanager
def db_connection(config=None):
    """
    数据库连接上下文管理器
    
    参数:
        config (dict): 数据库配置,如果为None则使用默认配置
        
    使用方法:
        with db_connection() as conn:
            # 使用连接执行操作
            pass
    """
    engine = get_db_engine(config)
    if engine is None:
        log_error("无法获取数据库引擎")
        yield None
        return
        
    connection = None
    try:
        # 从连接池获取连接
        connection = engine.connect()
        yield connection
    except Exception as e:
        log_error(f"数据库连接失败: {str(e)}")
        yield None
    finally:
        if connection:
            connection.close()

def get_mysql_connection(config=None):
    """
    获取MySQL数据库连接(兼容旧接口)
    
    参数:
        config (dict): 数据库配置,如果为None则使用默认配置
        
    返回:
        connection: 数据库连接对象
    """
    engine = get_db_engine(config)
    if engine is None:
        return None
        
    try:
        # 使用engine.raw_connection()获取原始DBAPI连接
        raw_conn = engine.raw_connection()
        return raw_conn
    except Exception as e:
        log_error(f"MySQL数据库连接失败: {str(e)}")
        return None

class MySQLConnectionWrapper:
    """
    MySQL连接包装器,将SQLAlchemy连接包装为类似pymysql的连接
    """
    def __init__(self, sqlalchemy_conn):
        self._conn = sqlalchemy_conn
        self._cursor = None
        
    def cursor(self):
        """创建游标"""
        if self._cursor is None:
            # 使用SQLAlchemy的engine.raw_connection()获取底层DBAPI连接
            self._cursor = self._conn.engine.raw_connection().cursor()
        return self._cursor
        
    def commit(self):
        """提交事务"""
        self._conn.commit()
        
    def rollback(self):
        """回滚事务"""
        self._conn.rollback()
        
    def close(self):
        """关闭连接"""
        if self._cursor:
            self._cursor.close()
            self._cursor = None
        self._conn.close()
        
    @property
    def user(self):
        return self._conn.engine.url.username
        
    @property
    def password(self):
        return self._conn.engine.url.password
        
    @property
    def host(self):
        return self._conn.engine.url.host
        
    @property
    def port(self):
        return self._conn.engine.url.port
        
    @property
    def db(self):
        return self._conn.engine.url.database
        
    def get_host_info(self):
        """获取主机信息(兼容pymysql接口)"""
        return f"{self.host} via TCP/IP"

def get_stock_codes_from_db(conn=None, market=None, industry=None):
    """
    从数据库获取股票代码
    
    参数:
        conn: 数据库连接,如果为None则创建新连接
        market (str): 市场类型,如'SZ'、'SH'
        industry (str): 所属行业
        
    返回:
        DataFrame: 包含股票代码和名称的数据框
    """
    close_conn = False
    if conn is None:
        conn = get_mysql_connection()
        close_conn = True
        
    if conn is None:
        return None
        
    try:
        # 构建SQL查询
        sql = "SELECT ts_code, stock_code, name, market, industry FROM stock_basic WHERE 1=1"
        
        # 检查连接类型并准备参数
        if hasattr(conn, 'execute'):
            # SQLAlchemy连接
            params = {}
            
            if market:
                sql += " AND market = :market"
                params['market'] = market
                
            if industry:
                sql += " AND industry = :industry"
                params['industry'] = industry
                
            # 使用pandas读取SQL
            df = pd.read_sql(text(sql), conn, params=params)
        else:
            # 原始DBAPI连接
            params = []
            
            if market:
                sql += " AND market = %s"
                params.append(market)
                
            if industry:
                sql += " AND industry = %s"
                params.append(industry)
                
            # 使用pandas读取SQL
            df = pd.read_sql(sql, conn, params=params)
            
        log_progress(f"从数据库获取到 {len(df)} 只股票信息")
        return df
    except Exception as e:
        log_error(f"从数据库获取股票代码失败: {str(e)}")
        return None
    finally:
        if close_conn and conn:
            conn.close()

def get_wind_code_from_ts_code(ts_code):
    """
    将ts_code转换为Wind代码格式
    
    参数:
        ts_code (str): ts代码格式,如'000001.SZ'
        
    返回:
        str: Wind代码格式,如'000001.SZ'(相同格式,但可能需要转换)
    """
    # ts_code和Wind代码格式可能相同,但如果有特殊转换需求可以在这里处理
    return ts_code

def get_friday_of_week(date):
    """
    获取指定日期所在周的周五日期
    
    参数:
        date (datetime): 日期对象
        
    返回:
        datetime: 周五的日期对象
    """
    # 0是周一,1是周二,2是周三,3是周四,4是周五,5是周六,6是周日
    days_to_friday = 4 - date.weekday()
    if days_to_friday < 0:
        days_to_friday += 7
    friday = date + timedelta(days=days_to_friday)
    return friday

def clear_table_data(conn, table_name):
    """
    清空表数据
    
    参数:
        conn: 数据库连接
        table_name: 表名
        
    返回:
        bool: 是否成功
    """
    try:
        # 检查连接类型并执行SQL
        if hasattr(conn, 'execute'):
            # SQLAlchemy连接
            sql = f"TRUNCATE TABLE {table_name}"
            conn.execute(text(sql))
            conn.commit()
        else:
            # 原始DBAPI连接
            cursor = conn.cursor()
            sql = f"TRUNCATE TABLE {table_name}"
            cursor.execute(sql)
            conn.commit()
            cursor.close()
            
        log_progress(f"已清空表 {table_name} 的数据")
        return True
    except Exception as e:
        log_error(f"清空表 {table_name} 数据失败: {str(e)}")
        try:
            if hasattr(conn, 'rollback'):
                conn.rollback()
        except:
            pass
        return False

def save_weekly_stock_data_to_db(conn, stock_data, ts_code, stock_name):
    """
    将周线股票数据保存到数据库
    
    参数:
        conn: 数据库连接
        stock_data: 从WindPy获取的股票数据
        ts_code: 股票的ts_code
        stock_name: 股票名称
        
    返回:
        int: 成功保存的记录数
    """
    if stock_data is None or len(stock_data) < 2:
        log_error("没有数据可以保存")
        return 0
        
    # 获取数据
    df = stock_data[1]
    
    # 提取股票代码(去掉后缀)
    stock_code = ts_code.split('.')[0]
    
    # 准备数据列表
    records = []
    
    for index, row in df.iterrows():
        # 处理日期
        trade_date = index.strftime('%Y-%m-%d') if hasattr(index, 'strftime') else str(index)
        
        # 准备记录
        record = {
            'ts_code': ts_code,
            'stock_code': stock_code,
            'name': stock_name,
            'trade_date': trade_date,
            'open': row.get('OPEN', None),
            'high': row.get('HIGH', None),
            'low': row.get('LOW', None),
            'close': row.get('CLOSE', None),
            'vol': row.get('VOLUME', None),
        }
        
        records.append(record)
    
    try:
        # 使用数据库连接池
        with db_connection() as db_conn:
            if db_conn is None:
                log_error("无法获取数据库连接")
                return 0
            
            # 将数据转换为DataFrame
            save_df = pd.DataFrame(records)
            
            # 保存到数据库
            save_df.to_sql(
                'ts_stock_weekly',
                db_conn,
                if_exists='append',
                index=False,
                chunksize=1000
            )
            
            log_progress(f"成功保存 {len(records)} 条周线记录到数据库")
            return len(records)
        
    except Exception as e:
        log_error(f"保存周线数据到数据库失败: {str(e)}")
        return 0

def retry_api_call(func, *args, max_retries=PROCESS_CONFIG['max_retries'],
                  retry_delay=PROCESS_CONFIG['retry_delay'], **kwargs):
    """
    带重试机制的API调用
    
    参数:
        func: 要调用的函数
        *args: 函数参数
        max_retries: 最大重试次数
        retry_delay: 重试延迟(秒)
        **kwargs: 函数关键字参数
        
    返回:
        函数调用结果或None(如果所有重试都失败)
    """
    last_error = None
    for retry in range(max_retries + 1):
        try:
            result = func(*args, **kwargs)
            
            # 检查结果是否有效
            if result is not None:
                if isinstance(result, tuple) and len(result) > 0 and hasattr(result[0], 'ErrorCode'):
                    if result[0].ErrorCode != 0:
                        raise Exception(f"API返回错误代码: {result[0].ErrorCode}")
                elif hasattr(result, 'ErrorCode') and result.ErrorCode != 0:
                    raise Exception(f"API返回错误代码: {result.ErrorCode}")
                    
                # 成功获取数据
                if retry > 0:
                    log_progress(f"第{retry+1}次重试成功")
                return result
                
        except Exception as e:
            last_error = str(e)
            if retry < max_retries:
                # 指数退避策略
                delay = retry_delay * (2 ** retry)
                log_error(f"第{retry+1}次调用失败: {last_error}, {delay}秒后重试")
                time.sleep(delay)
            else:
                log_error(f"所有重试均失败: {last_error}")
                
    return None

def get_weekly_stock_data(codes, fields, begin_time, end_time=None, options="", usedf=True):
    """
    获取股票周线历史数据
    
    参数:
        codes (str或list): 证券代码,如"000001.SZ"或["000001.SZ", "600000.SH"]
        fields (str或list): 指标列表,如"open,high,low,close,volume"
        begin_time (str或datetime): 起始日期
        end_time (str或datetime): 结束日期,默认为系统当前日期
        options (str): 选项参数
        usedf (bool): 是否返回DataFrame格式
        
    返回:
        WindData或DataFrame: 获取的数据
    """
    if end_time is None:
        end_time = datetime.now().strftime("%Y-%m-%d")
        
    # 简化日志输出
    log_progress(f"获取周线数据: {codes}")
    
    def _fetch_data():
        """内部数据获取函数"""
        # 根据配置决定是否检查Wind API连接
        if PROCESS_CONFIG['check_connection'] and not w.isconnected():
            log_progress("检测到Wind API未连接,尝试重新连接...")
            w.start()
            time.sleep(PROCESS_CONFIG['connection_wait'])  # 使用配置的等待时间
            
        # 设置周线参数
        weekly_options = f"Period=W;{options}" if options else "Period=W"
        
        # 使用wsd获取周线数据
        data = w.wsd(codes, fields, begin_time, end_time, weekly_options, usedf=usedf)
        
        # WindPy API返回的数据结构可能是元组或对象
        if isinstance(data, tuple):
            # 如果是元组,检查第一个元素是否有ErrorCode
            if len(data) > 0 and hasattr(data[0], 'ErrorCode'):
                if data[0].ErrorCode != 0:
                    raise Exception(f"API返回错误代码: {data[0].ErrorCode}")
            # 如果usedf=True,返回的元组第二个元素是DataFrame
            if len(data) >= 2 and usedf:
                return data
        elif hasattr(data, 'ErrorCode'):
            # 如果是对象,直接检查ErrorCode
            if data.ErrorCode != 0:
                raise Exception(f"API返回错误代码: {data.ErrorCode}")
            return data
        else:
            # 其他情况,假设数据获取成功
            return data
    
    # 使用重试机制获取数据
    result = retry_api_call(_fetch_data)
    
    if result is not None:
        log_progress("周线数据获取成功")
        # 添加API调用间隔,避免频繁调用
        if PROCESS_CONFIG['api_interval'] > 0:
            time.sleep(PROCESS_CONFIG['api_interval'])
    else:
        log_error("周线数据获取失败")
        
    return result

def process_weekly_stock_data_for_db(ts_code, name, fields, begin_date, end_date, options):
    """
    处理单只股票周线数据并保存到数据库
    
    参数:
        ts_code (str): 股票代码
        name (str): 股票名称
        fields (str): 指标列表
        begin_date (str): 开始日期
        end_date (str): 结束日期
        options (str): 选项参数
        
    返回:
        tuple: (success_count, ts_code, name) success_count为成功保存的记录数
    """
    try:
        # 简化日志输出
        log_progress(f"处理 {name}({ts_code})")
        
        # 获取股票周线数据
        data = get_weekly_stock_data(ts_code, fields, begin_date, end_date, options)
        
        if not data or len(data) < 2:
            log_error(f"获取 {name}({ts_code}) 周线数据失败")
            return (0, ts_code, name)
        
        # 处理数据
        df = data[1]
        stock_code = ts_code.split('.')[0]
        records = []
        
        # 获取最新日期
        latest_date = None
        if hasattr(df.index, 'max'):
            latest_date = df.index.max()
        
        for idx, df_row in df.iterrows():
            # 处理日期
            trade_date = idx.strftime('%Y-%m-%d') if hasattr(idx, 'strftime') else str(idx)
            
            # 如果是最新日期,则将其修改为所在周的周五日期
            if latest_date is not None and idx == latest_date:
                # 将索引转换为datetime对象
                if hasattr(idx, 'to_pydatetime'):
                    dt = idx.to_pydatetime()
                elif isinstance(idx, str):
                    dt = datetime.strptime(trade_date, '%Y-%m-%d')
                else:
                    dt = idx
                
                # 获取所在周的周五日期
                friday_date = get_friday_of_week(dt)
                trade_date = friday_date.strftime('%Y-%m-%d')
                # 移除日期修改日志
            
            # 准备记录
            record = {
                'ts_code': ts_code,
                'stock_code': stock_code,
                'name': name,
                'trade_date': trade_date,
                'open': df_row.get('OPEN', None),
                'high': df_row.get('HIGH', None),
                'low': df_row.get('LOW', None),
                'close': df_row.get('CLOSE', None),
                'vol': df_row.get('VOLUME', None),
            }
            
            records.append(record)
        
        # 保存到数据库
        if records:
            try:
                # 使用数据库连接池
                with db_connection() as conn:
                    if conn is None:
                        log_error(f"无法获取数据库连接")
                        return (0, ts_code, name)
                    
                    # 将数据转换为DataFrame
                    save_df = pd.DataFrame(records)
                    
                    # 保存到数据库
                    save_df.to_sql(
                        'ts_stock_weekly',
                        conn,
                        if_exists='append',
                        index=False,
                        chunksize=1000
                    )
                    
                    # 简化保存成功日志
                    log_progress(f"成功保存 {len(records)} 条周线记录")
                    return (len(records), ts_code, name)
            except Exception as e:
                log_error(f"保存 {name}({ts_code}) 周线数据到数据库失败: {str(e)}")
                return (0, ts_code, name)
        else:
            log_error(f"处理 {name}({ts_code}) 没有周线数据可保存")
            return (0, ts_code, name)
            
    except Exception as e:
        log_error(f"处理 {name}({ts_code}) 周线数据时出错: {e}")
        return (0, ts_code, name)

def process_all_weekly_stocks(batch_size=50, show_progress=True):
    """获取所有股票代码的周线数据并写入数据库
    
    参数:
        batch_size (int): 每批处理的股票数量,默认50只
        show_progress (bool): 是否显示进度信息,默认True
    """
    log_progress("===== 开始获取所有股票代码的周线数据并写入数据库 =====")
    
    # 使用数据库连接池
    try:
        # 获取原始DBAPI连接用于清空表
        raw_conn = get_mysql_connection()
        if raw_conn is None:
            log_error("无法连接到数据库,请检查数据库配置")
            return False
        
        # 从数据库获取所有股票代码
        log_progress("正在从数据库获取股票代码...")
        stocks_df = get_stock_codes_from_db(raw_conn)
        
        if stocks_df is None or len(stocks_df) == 0:
            log_error("无法从数据库获取股票代码")
            raw_conn.close()
            return False
            
        log_progress(f"从数据库获取到 {len(stocks_df)} 只股票信息")
        
        # 清空表数据
        log_progress("正在清空周线表数据...")
        if not clear_table_data(raw_conn, 'ts_stock_weekly'):
            log_error("清空周线表数据失败,处理终止")
            raw_conn.close()
            return False
        
        # 关闭原始连接
        raw_conn.close()
        
        # 设置获取字段和选项
        fields = "open,high,low,close,volume"
        options = "PriceAdj=F"  # 前复权
        begin_date = "2022-01-01"
        end_date = datetime.now().strftime("%Y-%m-%d")  # 设置为当前日期
        log_progress(f"周线数据获取时间范围: {begin_date} 至 {end_date}")
        
        total_saved = 0
        failed_stocks = []
        total_stocks = len(stocks_df)
        all_records = []  # 用于存储所有记录
        
        log_progress(f"开始处理股票周线数据,每批处理 {batch_size} 只股票...")
        
        # 分批处理股票数据
        for i in range(0, total_stocks, batch_size):
            batch_df = stocks_df.iloc[i:i+batch_size]
            log_progress(f"处理第{i//batch_size+1}批股票,共{len(batch_df)}只")
            
            batch_success = 0
            batch_failed = 0
            
            # 逐个处理股票
            for index, row in batch_df.iterrows():
                ts_code = row['ts_code']
                name = row['name']
                
                if show_progress:
                    # 修正进度计算逻辑
                    current_progress = (i // batch_size) * batch_size + index + 1
                    # 确保进度不超过总数
                    if current_progress > total_stocks:
                        current_progress = total_stocks
                    # 简化处理进度日志
                    log_progress(f"处理 {name}({ts_code}) [{current_progress}/{total_stocks}]")
                
                # 获取股票周线数据
                data = get_weekly_stock_data(ts_code, fields, begin_date, end_date, options)
                
                if data and len(data) >= 2:
                    # 获取数据并转换为记录格式
                    df = data[1]
                    stock_code = ts_code.split('.')[0]
                    
                    # 获取最新日期
                    latest_date = None
                    if hasattr(df.index, 'max'):
                        latest_date = df.index.max()
                    
                    for idx, df_row in df.iterrows():
                        # 处理日期
                        trade_date = idx.strftime('%Y-%m-%d') if hasattr(idx, 'strftime') else str(idx)
                        
                        # 如果是最新日期,则将其修改为所在周的周五日期
                        if latest_date is not None and idx == latest_date:
                            # 将索引转换为datetime对象
                            if hasattr(idx, 'to_pydatetime'):
                                dt = idx.to_pydatetime()
                            elif isinstance(idx, str):
                                dt = datetime.strptime(trade_date, '%Y-%m-%d')
                            else:
                                dt = idx
                            
                            # 获取所在周的周五日期
                            friday_date = get_friday_of_week(dt)
                            trade_date = friday_date.strftime('%Y-%m-%d')
                        
                        # 准备记录
                        record = {
                            'ts_code': ts_code,
                            'stock_code': stock_code,
                            'name': name,
                            'trade_date': trade_date,
                            'open': df_row.get('OPEN', None),
                            'high': df_row.get('HIGH', None),
                            'low': df_row.get('LOW', None),
                            'close': df_row.get('CLOSE', None),
                            'vol': df_row.get('VOLUME', None),
                        }
                        
                        all_records.append(record)
                    
                    batch_success += 1
                else:
                    failed_stocks.append({"ts_code": ts_code, "name": name})
                    batch_failed += 1
            
            if show_progress:
                # 简化批次完成日志
                log_progress(f"批次完成: 成功{batch_success}, 失败{batch_failed}")
        
        # 一次性保存所有数据到数据库
        if all_records:
            log_progress(f"正在保存 {len(all_records)} 条周线记录到数据库...")
            
            try:
                # 使用数据库连接池
                with db_connection() as save_conn:
                    if save_conn is None:
                        log_error("无法获取数据库连接")
                        return False
                    
                    # 将数据转换为DataFrame
                    save_df = pd.DataFrame(all_records)
                    
                    # 保存到数据库
                    save_df.to_sql(
                        'ts_stock_weekly',
                        save_conn,
                        if_exists='append',
                        index=False,
                        chunksize=1000
                    )
                    
                    total_saved = len(all_records)
                    log_progress(f"成功保存 {total_saved} 条周线记录到数据库")
            except Exception as e:
                log_error(f"保存周线数据到数据库失败: {str(e)}")
        else:
            log_error("没有周线数据可以保存")
        
        # 输出结果摘要
        log_progress("处理完成:")
        log_progress(f"- 总共处理了 {total_stocks} 只股票")
        log_progress(f"- 成功保存了 {total_saved} 条周线记录到数据库")
        log_progress(f"- {len(failed_stocks)} 只股票周线数据获取失败")
        
        # 仅当有失败股票且数量较少时显示详细信息
        if failed_stocks and len(failed_stocks) <= 10:
            log_progress("失败的股票列表:")
            for stock in failed_stocks:
                log_error(f"- {stock['name']}({stock['ts_code']})")
        elif failed_stocks:
            log_progress(f"有 {len(failed_stocks)} 只股票周线数据获取失败,详情请查看日志")
        
        return True
            
    except Exception as e:
        log_error(f"处理过程中发生错误: {str(e)}")
        return False

def test_five_weekly_stocks():
    """测试获取5个股票代码的周线数据并写入数据库"""
    log_progress("\n===== 测试获取5个股票代码的周线数据并写入数据库 =====")
    
    # 测试股票代码
    test_stocks = [
        {"ts_code": "000001.SZ", "name": "平安银行"},
        {"ts_code": "000002.SZ", "name": "万科A"},
        {"ts_code": "600000.SH", "name": "浦发银行"},
        {"ts_code": "600036.SH", "name": "招商银行"},
        {"ts_code": "000858.SZ", "name": "五粮液"}
    ]
    
    # 使用数据库连接池
    try:
        # 获取原始DBAPI连接用于清空表
        raw_conn = get_mysql_connection()
        if raw_conn is None:
            log_error("无法连接到数据库,请检查数据库配置")
            return False
        
        # 清空表数据
        log_progress("\n正在清空周线表数据...")
        if not clear_table_data(raw_conn, 'ts_stock_weekly'):
            log_error("清空周线表数据失败,测试终止")
            return False
        
        # 关闭原始连接
        raw_conn.close()
        
        # 设置获取字段和选项
        fields = "open,high,low,close,volume"
        options = "PriceAdj=F"  # 前复权
        begin_date = "2021-01-01"
        end_date = datetime.now().strftime("%Y-%m-%d")  # 设置为当前日期
        log_progress(f"测试周线数据获取时间范围: {begin_date} 至 {end_date}")
        
        total_saved = 0
        all_records = []
        
        # 第一步:获取所有股票周线数据
        for stock in test_stocks:
            ts_code = stock["ts_code"]
            name = stock["name"]
            
            log_progress(f"正在获取 {name}({ts_code}) 的周线数据...")
            
            # 获取股票周线数据
            data = get_weekly_stock_data(ts_code, fields, begin_date, end_date, options)
            
            if data and len(data) >= 2:
                # 获取数据并转换为记录格式
                df = data[1]
                stock_code = ts_code.split('.')[0]
                
                for index, row in df.iterrows():
                    # 处理日期
                    trade_date = index.strftime('%Y-%m-%d') if hasattr(index, 'strftime') else str(index)
                    
                    # 准备记录
                    record = {
                        'ts_code': ts_code,
                        'stock_code': stock_code,
                        'name': name,
                        'trade_date': trade_date,
                        'open': row.get('OPEN', None),
                        'high': row.get('HIGH', None),
                        'low': row.get('LOW', None),
                        'close': row.get('CLOSE', None),
                        'vol': row.get('VOLUME', None),
                    }
                    
                    all_records.append(record)
                    
                log_progress(f"已获取 {name} 的周线数据: {len(df)} 条记录")
            else:
                log_error(f"获取 {name}({ts_code}) 的周线数据失败")
        
        # 第二步:一次性保存所有数据到数据库
        if all_records:
            log_progress(f"正在保存 {len(all_records)} 条周线记录到数据库...")
            
            try:
                # 使用数据库连接池
                with db_connection() as save_conn:
                    if save_conn is None:
                        log_error("无法获取数据库连接")
                        return False
                    
                    # 将数据转换为DataFrame
                    save_df = pd.DataFrame(all_records)
                    
                    # 保存到数据库
                    save_df.to_sql(
                        'ts_stock_weekly',
                        save_conn,
                        if_exists='append',
                        index=False,
                        chunksize=1000
                    )
                    
                    total_saved = len(all_records)
                    log_progress(f"成功保存 {total_saved} 条周线记录到数据库")
            except Exception as e:
                log_error(f"保存周线数据到数据库失败: {str(e)}")
        else:
            log_error("没有周线数据可以保存")
        
        log_progress(f"测试完成,总共保存了 {total_saved} 条周线记录到数据库")
        return True
            
    except Exception as e:
        log_error(f"测试过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 启动WindPy API
    if start_wind_api():
        try:
            # 处理所有股票周线数据
            process_all_weekly_stocks(
                batch_size=500,
                show_progress=PROCESS_CONFIG['show_progress']
            )

            # 测试5只股票
            #test_five_weekly_stocks()
        finally:
            # 停止WindPy API
            stop_wind_api()