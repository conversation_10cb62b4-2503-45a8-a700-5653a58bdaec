#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据技术分析工具
使用Pyecharts绘制交互式图表,显示EMA和MACD等技术指标

依赖包安装:
pip install pyecharts pandas numpy scipy tushare

注意: 此版本使用Pyecharts替代了matplotlib,提供了更好的交互体验
"""

# 股票指数技术分析工具
# 提供数据获取、缓存、技术指标计算和可视化功能

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from scipy.signal import savgol_filter
from scipy.ndimage import gaussian_filter1d
from MyTT import EMA   # 引入 MyTT 库中 EMA 指标计算函数
import tushare as ts
from datetime import datetime
import json
import os
from pathlib import Path
import argparse
import sys
import configparser
import matplotlib as mpl
from matplotlib.font_manager import FontProperties, findfont
import platform
import pyecharts.options as opts
from pyecharts.charts import Line, Grid, Bar, Scatter
from pyecharts.commons.utils import JsCode
from pyecharts.components import Table

# 移除configure_matplotlib_fonts函数,不再需要
def configure_matplotlib_fonts():
    """Configure matplotlib fonts"""
    plt.rcParams['font.family'] = ['sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    return True

class StockDataManager:
    """处理股票数据获取和缓存的类"""
    
    def __init__(self, api_key=None):
        """初始化StockDataManager
        
        Args:
            api_key: Tushare API密钥,如果为None则尝试从配置文件加载
        """
        self.api_key = api_key or self._load_api_key()
        if not self.api_key:
            raise ValueError("未提供API密钥,请在配置文件中设置或作为参数传入")
            
        self.cache_dir = Path("cache")
        self.cache_dir.mkdir(exist_ok=True)
    
    def _load_api_key(self):
        """从配置文件加载API密钥"""
        config = configparser.ConfigParser()
        config_path = Path("config.ini")
        
        # 默认API密钥,仅作为示例
        default_key = '5aba669a485730beded0d604bd8ccfdaca39f8c9c09ef2d0ed78ed77'
        
        if config_path.exists():
            try:
                config.read(config_path)
                return config.get('API', 'tushare_token', fallback=default_key)
            except Exception as e:
                print(f"读取配置文件失败: {str(e)}")
                return default_key
        else:
            # 创建默认配置文件
            config['API'] = {'tushare_token': default_key}
            with open(config_path, 'w') as f:
                config.write(f)
            print(f"已创建默认配置文件: {config_path}")
            return default_key
    
    def get_cache_path(self, ts_code):
        """获取缓存文件路径
        
        Args:
            ts_code: 股票或指数代码
            
        Returns:
            Path对象表示缓存文件路径
        """
        return self.cache_dir / f"{ts_code}_data.json"
    
    def is_cache_valid(self, cache_path):
        """检查缓存是否在当天有效
        
        Args:
            cache_path: 缓存文件路径
            
        Returns:
            布尔值表示缓存是否有效
        """
        if not cache_path.exists():
            return False
        
        try:
            with open(cache_path, 'r') as f:
                cache_data = json.load(f)
            cache_date = datetime.strptime(cache_data['timestamp'], '%Y%m%d').date()
            today = datetime.now().date()
            return cache_date == today
        except:
            return False

    def get_stock_data(self, ts_code="IXIC", start_date=None, end_date=None, force_refresh=False):
        """使用Tushare API获取指数数据,带缓存机制
        
        Args:
            ts_code: 指数代码,默认为纳斯达克指数'IXIC'
            start_date: 开始日期(可选)
            end_date: 结束日期(可选)
            force_refresh: 是否强制刷新缓存数据,默认False
            
        Returns:
            返回股票数据DataFrame
        """
        cache_path = self.get_cache_path(ts_code)
    
        # 检查是否存在当天的有效缓存且不强制刷新
        if not force_refresh and self.is_cache_valid(cache_path):
            try:
                with open(cache_path, 'r') as f:
                    cache_data = json.load(f)
                print(f"使用缓存数据 ({ts_code})")
                return pd.DataFrame(cache_data['data'])
            except Exception as e:
                print(f"读取缓存失败: {str(e)}")
    
        try:
            # 初始化pro接口
            pro = ts.pro_api(self.api_key)
        
            # 构建查询参数
            today = datetime.now().strftime('%Y%m%d')
            params = {
                "ts_code": ts_code,
                "start_date": start_date if start_date else "20240101",
                "end_date": end_date if end_date else today,
            }
            
            # 获取数据
            df = pro.index_global(**params, fields="ts_code,trade_date,open,close,high,low,vol")
            
            if not df.empty:
                # 重命名volume列以匹配原代码
                df = df.rename(columns={'vol': 'volume'})
                
                # 保存到缓存
                cache_data = {
                    'timestamp': today,
                    'data': df.to_dict('records')
                }
                with open(cache_path, 'w') as f:
                    json.dump(cache_data, f)
                print(f"已更新缓存数据 ({ts_code})")
                
                # 数据预处理 - 先按时间升序排列以便正确计算指标
                df = df.sort_values('trade_date', ascending=True)
                
                # 过滤日期范围
                if end_date:
                    df = df[df['trade_date'] <= end_date]
                
                # 提取收盘价数据并计算指标
                y = df['close'].values
                
                return df
            else:
                print(f"未找到指数 {ts_code} 在指定日期区间的数据")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"获取数据失败: {str(e)}")
            return pd.DataFrame()

    def get_available_indices(self):
        """获取可用的指数列表"""
        return {
            "SPX": "标普500指数",
            "IXIC": "纳斯达克指数",
            "GDAXI": "德国DAX指数",
            "FTSE": "英国富时100指数",
            "FCHI": "法国CAC40指数",
            "N225": "日经225指数",
            "HSI": "恒生指数"
        }


class TechnicalIndicator:
    """计算技术指标的类"""
    
    @staticmethod
    def calculate_ema(data, long_term=26, short_term=13):
        """计算长短期EMA及其差值
        
        Args:
            data: 收盘价数据数组
            long_term: 长期EMA周期数
            short_term: 短期EMA周期数
            
        Returns:
            tuple: (长期EMA, 短期EMA, 差值, 对齐偏移量)
        """
        ema_long = EMA(data, long_term)
        ema_short = EMA(data, short_term)
        
        # 对齐:跳过EMA计算初期的偏移(longterm-1个数据)
        align_offset = long_term - 1
        ema_long_aligned = ema_long[align_offset:]
        ema_short_aligned = ema_short[align_offset:]
        
        # 差值数组
        ema_difference = ema_short_aligned - ema_long_aligned
        
        return ema_long, ema_short, ema_difference, align_offset
    
    @staticmethod
    def calculate_macd(data, fast=12, slow=26, signal=9):
        """计算MACD指标
        
        Args:
            data: 收盘价数据
            fast: 快线周期
            slow: 慢线周期
            signal: 信号线周期
            
        Returns:
            tuple: (DIF线, DEA信号线, MACD柱状图)
        """
        # 确保数据是numpy数组类型
        data = np.array(data)
        
        # 计算快线和慢线EMA
        ema_fast = EMA(data, fast)
        ema_slow = EMA(data, slow)
        
        # 计算DIF
        dif = np.array(ema_fast) - np.array(ema_slow)
        
        # 计算DEA信号线
        dea = EMA(dif, signal)
        
        # 计算MACD柱状图
        macd = (np.array(dif) - np.array(dea)) * 2
        
        return dif, dea, macd
    
    @staticmethod
    def smooth_data(data, method='savgol', window_size=None, poly_order=3, sigma=2):
        """平滑处理数据
        
        Args:
            data: 需要平滑的数据数组
            method: 平滑方法,可选 'savgol'(Savitzky-Golay滤波), 'ma'(移动平均), 
                   'ewma'(指数加权移动平均), 'gaussian'(高斯平滑)
            window_size: 窗口大小,如果为None则自动计算
            poly_order: 多项式阶数(仅用于savgol)
            sigma: 高斯平滑的标准差(仅用于gaussian)
            
        Returns:
            平滑后的数据
        """
        data_len = len(data)
        
        # 如果数据长度不足,返回原始数据
        if data_len < 5:
            return data
        
        # 自动计算窗口大小(如果未指定)
        if window_size is None:
            window_size = min(int(data_len * 0.15), 51)
            # 确保窗口大小为奇数
            if window_size % 2 == 0:
                window_size += 1
            
        # 确保窗口大小不超过数据长度
        window_size = min(window_size, data_len - 2)
        if window_size < 3:
            return data
            
        # 确保窗口大小为奇数(savgol必须是奇数)
        if window_size % 2 == 0:
            window_size += 1
            
        # 根据选择的方法进行平滑处理
        if method == 'savgol':
            # Savitzky-Golay滤波
            poly_order = min(poly_order, window_size - 1)  # 确保多项式阶数有效
            return savgol_filter(data, window_size, poly_order)
            
        elif method == 'ma':
            # 简单移动平均
            weights = np.ones(window_size) / window_size
            return np.convolve(data, weights, mode='same')
            
        elif method == 'ewma':
            # 指数加权移动平均
            alpha = 2 / (window_size + 1)  # 平滑因子
            return pd.Series(data).ewm(alpha=alpha, adjust=False).mean().values
            
        elif method == 'gaussian':
            # 高斯平滑
            return gaussian_filter1d(data, sigma=sigma)
            
        else:
            # 默认返回原始数据
            return data


class StockEchartVisualizer:
    """使用Pyecharts绘制股票图表的类"""
    
    def __init__(self, config=None):
        """初始化StockEchartVisualizer
        
        Args:
            config: 配置信息字典,可包含主题、大小等设置
        """
        self.data_manager = StockDataManager()
        self.theme = 'white'  # Pyecharts主题
        self.width = 1200
        self.height = 800
        self.js_renderer = 'canvas'  # 渲染模式:canvas 或 svg
        
        if config:
            self.theme = config.get('theme', self.theme)
            self.width = config.get('width', self.width)
            self.height = config.get('height', self.height)
            self.js_renderer = config.get('renderer', self.js_renderer)
    
    def set_theme(self, theme):
        """设置图表主题
        
        Args:
            theme: Pyecharts支持的主题
        """
        self.theme = theme
        
    def set_size(self, width, height):
        """设置图表大小
        
        Args:
            width: 图表宽度
            height: 图表高度
        """
        self.width = width
        self.height = height
        
    def plot_stock_chart(self, ts_code="IXIC", display_points=250, force_refresh=False,
                         long_term=26, short_term=13, save_path=None, 
                         smooth_method='savgol', smooth_window=None, poly_order=3,
                         start_date=None, end_date=None):
        """使用Pyecharts绘制股票图表,展示技术指标
        
        Args:
            ts_code: 指数代码,默认为纳斯达克指数'IXIC'
            display_points: 要显示的数据点数量
            force_refresh: 是否强制刷新数据
            long_term: 长期EMA周期
            short_term: 短期EMA周期
            save_path: 图表保存路径,如果为None则不保存
            smooth_method: 平滑方法,可选 'savgol', 'ma', 'ewma', 'gaussian'
            smooth_window: 平滑窗口大小,为None则自动计算
            poly_order: 多项式阶数(用于savgol)
            start_date: 开始日期,格式:YYYYMMDD
            end_date: 结束日期,格式:YYYYMMDD
            
        Returns:
            Pyecharts Grid对象
        """
        # 获取股票数据
        df = self.data_manager.get_stock_data(ts_code, start_date, end_date, force_refresh)

        # 检查数据是否为空
        if df.empty:
            raise ValueError("数据为空,请检查指数代码或数据源.")

        # 数据预处理 - 按时间升序排列
        df = df.sort_values('trade_date', ascending=True)
        
        # 过滤日期范围
        if end_date:
            df = df[df['trade_date'] <= end_date]
        
        # 提取收盘价数据并计算指标
        close_prices = df['close'].values
        
        # 计算技术指标
        ema_long, ema_short, ema_difference, align_offset = TechnicalIndicator.calculate_ema(
            close_prices, long_term=long_term, short_term=short_term
        )
        
        # 计算5日EMA
        ema_5 = EMA(close_prices, 5)
        
        # 对齐:跳过EMA计算初期的偏移
        ema_long_aligned = ema_long[align_offset:]
        ema_short_aligned = ema_short[align_offset:]
        ema_5_aligned = ema_5[align_offset:]  # 对齐5日EMA
        
        # 设置图像显示的数据点数量
        data_length = len(close_prices)
        start_idx = max(0, data_length - display_points)
        
        # 获取要显示的日期范围
        dates = df['trade_date'].values
        display_dates = dates[start_idx:]
        
        # 为echarts准备数据
        x_data = [str(date) for date in display_dates]
        
        # 价格图表数据
        price_data = close_prices[start_idx:].tolist()
        
        # 对齐的EMA数据
        x_aligned = np.arange(align_offset, len(close_prices))
        display_mask = x_aligned >= start_idx
        
        # 获取显示范围内的EMA数据
        display_indices = x_aligned[display_mask] - start_idx
        ema_long_display = ema_long_aligned[display_mask].tolist()
        ema_short_display = ema_short_aligned[display_mask].tolist()
        
        # 差值图表数据准备
        ema_diff_raw = (ema_short_aligned - ema_long_aligned)
        
        # 计算差值的变化率(差分)
        ema_diff_delta = np.diff(ema_diff_raw)
        x_diff = x_aligned[:-1]  # 差分后序列长度减1
        delta_display_mask = x_diff >= start_idx
        
        # 创建一个包含所有日期的索引,方便后续映射
        date_indices = {date: i for i, date in enumerate(x_data)}
        
        # 对差分数据进行平滑处理,使信号更清晰
        window = min(15, len(ema_diff_delta) // 8)  # 较小的窗口使信号更敏感
        if window % 2 == 0:
            window += 1  # 确保为奇数
            
        # 创建更清晰的动量指标
        if len(ema_diff_delta) > window:
            momentum = TechnicalIndicator.smooth_data(
                ema_diff_delta, method='ewma', window_size=window
            )
        else:
            momentum = ema_diff_delta
            
        # 自适应缩放因子,增强可视性但不过度放大
        delta_max = max(abs(np.max(ema_diff_delta)), abs(np.min(ema_diff_delta)))
        if delta_max > 0:
            scale_factor = min(300 / delta_max, 15)  # 适度缩放,避免过度放大噪声
        else:
            scale_factor = 10
            
        # 平滑处理后的差值
        smoothed_diff = TechnicalIndicator.smooth_data(
            ema_diff_raw, method=smooth_method, window_size=smooth_window, 
            poly_order=poly_order
        )
        
        # 寻找买卖信号点
        crossover = (momentum[:-1] <= 0) & (momentum[1:] > 0)  # 由负转正
        crossunder = (momentum[:-1] >= 0) & (momentum[1:] < 0)  # 由正转负
        
        buy_points = []
        sell_points = []
        signal_data = []
        
        if len(x_diff[delta_display_mask]) > 1:
            for i in range(1, len(momentum[delta_display_mask])):
                idx = np.where(x_diff == x_diff[delta_display_mask][i])[0][0]
                if idx > 0 and idx < len(crossover):
                    date_str = str(dates[start_idx + i])
                    price = close_prices[start_idx + i]
                    current_trend = ema_diff_raw[idx]
                    
                    if crossover[idx]:  # 买入信号
                        buy_points.append((date_str, price))
                        signal_data.append([date_str, "买入", f"{price:.2f}"])
                        print(f"Buy Signal found at {date_str}, price: {price:.2f}, trend: {current_trend:.4f}")
                    elif crossunder[idx]:  # 卖出信号
                        sell_points.append((date_str, price))
                        signal_data.append([date_str, "卖出", f"{price:.2f}"])
                        print(f"Sell Signal found at {date_str}, price: {price:.2f}, trend: {current_trend:.4f}")
        
        # 按日期从新到旧排序
        if signal_data:
            signal_data.sort(key=lambda x: x[0], reverse=True)
            # 只保留最近的6个信号点
            signal_data = signal_data[:6]
        
        # 使用单一图例配置,调整图例位置和显示
        price_chart = (
            Line()
            .add_xaxis(x_data)
            .add_yaxis(
                "价格", 
                price_data,
                is_smooth=True,
                linestyle_opts=opts.LineStyleOpts(width=1, opacity=0.6),
                label_opts=opts.LabelOpts(is_show=False),
            )
            .set_global_opts(
                # 为顶部图表添加总标题
                title_opts=opts.TitleOpts(
                    title="趋势分析",
                    subtitle=f"{ts_code}",
                    pos_left="center",
                    pos_top="0px",
                    title_textstyle_opts=opts.TextStyleOpts(
                        font_size=18,
                        font_weight="bold",
                        color="#333"
                    ),
                    subtitle_textstyle_opts=opts.TextStyleOpts(
                        font_size=14,
                        color="#666"
                    )
                ),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    axislabel_opts=opts.LabelOpts(is_show=False),  # 隐藏第一个图的x轴标签
                    is_scale=True,
                ),
                yaxis_opts=opts.AxisOpts(
                    name="价格",
                    is_scale=True,  # 确保y轴不从0开始
                    axistick_opts=opts.AxisTickOpts(is_show=True),
                    splitline_opts=opts.SplitLineOpts(is_show=True),
                    min_="dataMin",  # 设置y轴最小值为数据最小值
                ),
                tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
                # 移动图例到左上角并增加透明度
                legend_opts=opts.LegendOpts(
                    pos_left="5%",
                    pos_top="5%",
                    orient="horizontal",
                    item_gap=15,
                    item_width=25,
                    item_height=14,
                    border_width=0,
                    textstyle_opts=opts.TextStyleOpts(font_size=12),
                    background_color="rgba(255, 255, 255, 0.6)",  # 半透明白色背景
                    padding=8,
                ),
            )
        )
        
        # 添加EMA曲线
        # 获取显示范围内的所有EMA数据
        ema_long_series = np.zeros(len(x_data))
        ema_short_series = np.zeros(len(x_data))
        ema_5_series = np.zeros(len(x_data))  # 添加5日EMA数据数组
        
        # 将对应位置填充实际数据
        for i, idx in enumerate(x_aligned[display_mask]):
            relative_idx = idx - start_idx
            if 0 <= relative_idx < len(x_data):
                ema_long_series[relative_idx] = ema_long_aligned[display_mask][i]
                ema_short_series[relative_idx] = ema_short_aligned[display_mask][i]
                ema_5_series[relative_idx] = ema_5_aligned[display_mask][i]  # 填充5日EMA数据
        
        # 对未填充的位置做线性插值(确保连续线条)
        has_data = np.where(ema_long_series != 0)[0]
        if len(has_data) > 0:
            first_valid = has_data[0]
            last_valid = has_data[-1]
            
            # 填充长期EMA和短期EMA
            price_chart.add_yaxis(
                f"{long_term}日EMA",
                # 只取有效数据范围
                ema_long_series[first_valid:last_valid+1].tolist(),
                is_symbol_show=False,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(width=2),
            )
            
            price_chart.add_yaxis(
                f"{short_term}日EMA", 
                # 只取有效数据范围
                ema_short_series[first_valid:last_valid+1].tolist(),
                is_symbol_show=False,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(width=2),
            )
            
            # 添加5日EMA
            price_chart.add_yaxis(
                "5日EMA", 
                # 只取有效数据范围
                ema_5_series[first_valid:last_valid+1].tolist(),
                is_symbol_show=False,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(width=2, color="#FF9900"),  # 使用橙色以区分其他线
            )
            
            # 也需要修改x轴数据
            if first_valid > 0 or last_valid < len(x_data) - 1:
                # 为了调整X轴,重新创建图表
                adjusted_x_data = x_data[first_valid:last_valid+1]
                
                # 临时保存已添加的其他系列
                series_data = price_chart.options.get("series", [])
                first_series = series_data[0] if series_data else None
                
                # 更新主价格图表的X轴数据
                price_chart.options["xAxis"][0]["data"] = adjusted_x_data
                
                # 如果需要,也可以调整价格数据
                if first_series:
                    first_series["data"] = price_data[first_valid:last_valid+1]
        
        # 添加买卖点
        buy_scatter = Scatter()
        if buy_points:
            buy_x = []
            buy_y = []
            for date, price in buy_points:
                if date in date_indices:
                    buy_x.append(date)
                    buy_y.append(price)
            if buy_x:
                buy_scatter.add_xaxis(buy_x)
                buy_scatter.add_yaxis(
                    "买入信号",
                    buy_y,
                    symbol_size=15,
                    symbol="triangle",
                    itemstyle_opts=opts.ItemStyleOpts(color="green"),
                    label_opts=opts.LabelOpts(is_show=False),
                )
                price_chart.overlap(buy_scatter)
        
        sell_scatter = Scatter()
        if sell_points:
            sell_x = []
            sell_y = []
            for date, price in sell_points:
                if date in date_indices:
                    sell_x.append(date)
                    sell_y.append(price)
            if sell_x:
                sell_scatter.add_xaxis(sell_x)
                sell_scatter.add_yaxis(
                    "卖出信号",
                    sell_y,
                    symbol_size=15,
                    symbol="triangle-down",
                    itemstyle_opts=opts.ItemStyleOpts(color="red"),
                    label_opts=opts.LabelOpts(is_show=False),
                )
                price_chart.overlap(sell_scatter)
        
        # 创建动量指标图表
        momentum_data = []
        momentum_dates = []
        positive_momentum = []
        negative_momentum = []
        
        for i, idx in enumerate(x_diff[delta_display_mask]):
            if idx - start_idx >= 0 and idx - start_idx < len(x_data):
                momentum_dates.append(x_data[idx - start_idx])
                momentum_value = momentum[delta_display_mask][i] * scale_factor
                momentum_data.append(momentum_value)
                
                if momentum_value >= 0:
                    positive_momentum.append(momentum_value)
                    negative_momentum.append(0)
                else:
                    positive_momentum.append(0)
                    negative_momentum.append(momentum_value)
        
        # 添加正负区域填充,避免图例显示问题
        pos_line = (
            Line()
            .add_xaxis(momentum_dates)
            .add_yaxis(
                "", # 使用空字符串作为名称,隐藏图例项
                positive_momentum,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(width=0),
                areastyle_opts=opts.AreaStyleOpts(color="green", opacity=0.3),
                is_symbol_show=False,
            )
        )
        
        neg_line = (
            Line()
            .add_xaxis(momentum_dates)
            .add_yaxis(
                "", # 使用空字符串作为名称,隐藏图例项
                negative_momentum,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(width=0),
                areastyle_opts=opts.AreaStyleOpts(color="red", opacity=0.3),
                is_symbol_show=False,
            )
        )

        # 动量图表配置
        momentum_chart = (
            Line()
            .add_xaxis(momentum_dates)
            .add_yaxis(
                "动量指标", 
                momentum_data,
                is_smooth=True,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(width=1.5, color="blue"),
                areastyle_opts=opts.AreaStyleOpts(opacity=0),
            )
            .set_global_opts(
                # 去除标题,避免重叠问题
                title_opts=opts.TitleOpts(title=""),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    axislabel_opts=opts.LabelOpts(is_show=False),
                    is_scale=True,
                ),
                yaxis_opts=opts.AxisOpts(
                    name="动量",
                    is_scale=True,
                    axistick_opts=opts.AxisTickOpts(is_show=True),
                    splitline_opts=opts.SplitLineOpts(is_show=True),
                ),
                tooltip_opts=opts.TooltipOpts(trigger="axis"),
                # 移动图例到左侧并增加透明度
                legend_opts=opts.LegendOpts(
                    pos_left="5%",
                    pos_top="40%",
                    orient="horizontal",
                    item_gap=15,
                    item_width=25,
                    item_height=14,
                    border_width=0,
                    textstyle_opts=opts.TextStyleOpts(font_size=12),
                    background_color="rgba(255, 255, 255, 0.6)",  # 半透明白色背景
                    padding=8,
                ),
            )
            .set_series_opts(
                markline_opts=opts.MarkLineOpts(
                    data=[opts.MarkLineItem(y=0, name="基准线")],
                    linestyle_opts=opts.LineStyleOpts(color="black", width=1, opacity=0.3),
                )
            )
        )

        momentum_chart.overlap(pos_line)
        momentum_chart.overlap(neg_line)
        
        # 创建趋势差异图表
        trend_data = []
        trend_dates = []
        
        for i, idx in enumerate(x_aligned[display_mask]):
            if idx - start_idx >= 0 and idx - start_idx < len(x_data):
                trend_dates.append(x_data[idx - start_idx])
                trend_data.append(ema_diff_raw[display_mask][i] * 2)  # 乘以2增强可视性
        
        smoothed_data = []
        
        for i, idx in enumerate(x_aligned[display_mask]):
            if idx - start_idx >= 0 and idx - start_idx < len(x_data):
                smoothed_data.append(smoothed_diff[display_mask][i] * 2)
        
        trend_chart = (
            Line()
            .add_xaxis(trend_dates)
            .add_yaxis(
                "短长期差值", 
                trend_data,
                is_smooth=True,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(width=1, opacity=0.5, color="purple"),
            )
            .add_yaxis(
                f"{smooth_method}平滑", 
                smoothed_data,
                is_smooth=True,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(width=2, color="blue"),
            )
            .set_global_opts(
                # 去除标题,避免重叠问题
                title_opts=opts.TitleOpts(title=""),
                xaxis_opts=opts.AxisOpts(
                    type_="category",
                    axislabel_opts=opts.LabelOpts(rotate=45, interval=int(len(trend_dates)/15)),
                    is_scale=True,
                ),
                yaxis_opts=opts.AxisOpts(
                    name="趋势差异",
                    is_scale=True,
                    axistick_opts=opts.AxisTickOpts(is_show=True),
                    splitline_opts=opts.SplitLineOpts(is_show=True),
                ),
                tooltip_opts=opts.TooltipOpts(trigger="axis"),
                # 移动图例到左侧并增加透明度
                legend_opts=opts.LegendOpts(
                    pos_left="5%",
                    pos_top="75%",
                    orient="horizontal",
                    item_gap=15,
                    item_width=25,
                    item_height=14,
                    border_width=0,
                    textstyle_opts=opts.TextStyleOpts(font_size=12),
                    background_color="rgba(255, 255, 255, 0.6)",  # 半透明白色背景
                    padding=8,
                ),
            )
            .set_series_opts(
                markline_opts=opts.MarkLineOpts(
                    data=[opts.MarkLineItem(y=0, name="基准线")],
                    linestyle_opts=opts.LineStyleOpts(color="black", width=1, opacity=0.3),
                )
            )
        )
        
        # 添加其他平滑线作为参考
        if smooth_method == 'savgol' and poly_order > 0:
            # 低阶平滑参考线
            lower_order = max(0, poly_order - 1)
            smoothed_diff_lower_order = TechnicalIndicator.smooth_data(
                ema_diff_raw, method='savgol', window_size=smooth_window, poly_order=lower_order
            )
            
            lower_order_data = []
            for i, idx in enumerate(x_aligned[display_mask]):
                if idx - start_idx >= 0 and idx - start_idx < len(x_data):
                    lower_order_data.append(smoothed_diff_lower_order[display_mask][i] * 2)
            
            trend_chart.add_yaxis(
                f"Savgol({lower_order}阶)", 
                lower_order_data,
                is_smooth=True,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(width=1.5, opacity=0.7, color="cyan"),
            )
        
        # 添加EWMA作为参考
        if smooth_method != 'ma' and smooth_method != 'ewma':
            ewma_line = TechnicalIndicator.smooth_data(
                ema_diff_raw, method='ewma', window_size=smooth_window
            )
            
            ewma_data = []
            for i, idx in enumerate(x_aligned[display_mask]):
                if idx - start_idx >= 0 and idx - start_idx < len(x_data):
                    ewma_data.append(ewma_line[display_mask][i] * 2)
            
            trend_chart.add_yaxis(
                "EWMA参考", 
                ewma_data,
                is_smooth=True,
                label_opts=opts.LabelOpts(is_show=False),
                linestyle_opts=opts.LineStyleOpts(width=1.5, opacity=0.5, color="green", type_="dashed"),
            )
        
        # 创建Grid布局
        grid = Grid(init_opts=opts.InitOpts(
            width=f"{self.width}px", 
            height=f"{self.height}px",
            theme=self.theme,
            renderer=self.js_renderer,
            # 设置页面标题(浏览器标签页标题)
            page_title="趋势分析"
        ))
        
        # 设置三个图表的位置,调整顶部间距留出更多空间给标题和图例
        grid.add(
            price_chart,
            grid_opts=opts.GridOpts(pos_top="15%", pos_bottom="70%", pos_left="5%", pos_right="3%"),
        )
        grid.add(
            momentum_chart,
            grid_opts=opts.GridOpts(pos_top="35%", pos_bottom="40%", pos_left="5%", pos_right="3%"),
        )
        grid.add(
            trend_chart,
            grid_opts=opts.GridOpts(pos_top="65%", pos_bottom="10%", pos_left="5%", pos_right="3%"),
        )
        
        # 如果提供了保存路径,保存图表
        if save_path:
            grid.render(save_path)
            print(f"图表已保存到: {save_path}")

        return grid


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Stock Index Technical Analysis Tool')
    
    parser.add_argument('--index', '-i', type=str, default='IXIC',
                        help='Index code, e.g., IXIC (NASDAQ), SPX (S&P 500)')
    parser.add_argument('--points', '-p', type=int, default=200,
                        help='Number of data points to display')
    parser.add_argument('--refresh', '-r', action='store_true',
                        help='Force refresh data')
    parser.add_argument('--long', '-l', type=int, default=26,
                        help='Long-term EMA period')
    parser.add_argument('--short', '-s', type=int, default=13,
                        help='Short-term EMA period')
    parser.add_argument('--save', type=str, default=None,
                        help='Path to save the chart')
    parser.add_argument('--list', action='store_true',
                        help='List all available index codes')
    parser.add_argument('--style', type=str, default='seaborn-v0_8-darkgrid',
                        help='Chart style')
    parser.add_argument('--smooth', type=str, choices=['savgol', 'ma', 'ewma', 'gaussian'], 
                        default='savgol', help='Smoothing method')
    parser.add_argument('--window', type=int, default=None, 
                        help='Smoothing window size (None for auto)')
    parser.add_argument('--order', type=int, default=3, 
                        help='Savitzky-Golay filter polynomial order')
    parser.add_argument('--start-date', type=str, default="20240101",
                        help='Start date in YYYYMMDD format')
    parser.add_argument('--end-date', type=str, default=None,
                        help='End date in YYYYMMDD format')
    
    return parser.parse_args()


def main():
    """主函数,程序入口点"""
    args = parse_arguments()
    
    # 创建数据管理器
    data_manager = StockDataManager()
    
    # 如果请求列出可用的指数代码
    if args.list:
        indices = data_manager.get_available_indices()
        print("可用的指数代码:")
        for code, name in indices.items():
            print(f"{code}: {name}")
        return
    
    # 创建可视化器
    config = {
        "theme": args.style if args.style in ['white', 'dark'] else 'white',
        "width": 1200,
        "height": 800
    }
    
    # 使用Pyecharts可视化器
    visualizer = StockEchartVisualizer(config)
    
        # 绘制图表
    chart = visualizer.plot_stock_chart(
            ts_code=args.index,
            display_points=args.points,
            force_refresh=args.refresh,
            long_term=args.long, 
            short_term=args.short,
            save_path=args.save,
            smooth_method=args.smooth,
            smooth_window=args.window,
            poly_order=args.order,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    # 如果没有指定保存路径,则在浏览器中显示
    if not args.save:
        temp_path = "temp_chart.html"
        chart.render(temp_path)
        import webbrowser
        webbrowser.open(f"file://{os.path.abspath(temp_path)}")
        print(f"图表已在浏览器中打开,临时文件: {temp_path}")


# 当脚本直接运行时执行main函数
if __name__ == "__main__":
    # 使用示例:
    # python indicator_analysis.py --index IXIC --points 120 --long 26 --short 13 --smooth savgol --window 51 --order 3 --start-date 20240101 --end-date 20250224
    # 
    # 可用参数:
    # --index, -i: 指数代码,如IXIC(纳斯达克指数)、SPX(标普500)
    # --points, -p: 显示的数据点数量
    # --refresh, -r: 强制刷新数据,不使用缓存
    # --long, -l: 长期EMA周期,默认26
    # --short, -s: 短期EMA周期,默认13
    # --save: 保存图表的路径,如"chart.png"
    # --list: 列出所有可用的指数代码
    # --style: 图表样式,默认为seaborn-v0_8-darkgrid
    # --smooth: 平滑方法选择(savgol, ma, ewma, gaussian)
    # --window: 平滑窗口大小(None为自动)
    # --order: Savitzky-Golay滤波器的多项式阶数
    # --start-date: 起始日期,格式YYYYMMDD,默认20240101
    # --end-date: 结束日期,格式YYYYMMDD,默认为当天
    
    # --------------------
    # 直接修改下面的参数来运行脚本
    # --------------------
    run_with_args = False  # 设置为True使用命令行参数,False使用下面的参数
    
    if run_with_args:
        main()  # 使用命令行参数运行
    else:
        # 直接在这里修改参数
        #"SPX": "标普500指数",
        #"IXIC": "纳斯达克指数",
        #"GDAXI": "德国DAX指数",
        #"FTSE": "英国富时100指数",
        #"FCHI": "法国CAC40指数",
        #"N225": "日经225指数",
        #"HSI": "恒生指数"
        ts_code = "IXIC"          # 指数代码
        display_points = 200      # 显示点数
        force_refresh = True     # 是否强制刷新数据
        long_term = 26            # 长期EMA周期
        short_term = 13           # 短期EMA周期
        save_path = None          # 保存图表路径,如 "chart.png"
        style = "seaborn-v0_8-darkgrid"  # 图表样式
        use_english = True        # 是否使用英文界面(如果中文显示有问题,请设置为True)
        
        # 日期参数
        start_date = "20240101"   # 起始日期,格式:YYYYMMDD
        end_date = "20250224"     # 结束日期,格式:YYYYMMDD
        
        # 平滑参数
        smooth_method = "gaussian"  # 平滑方法: 'savgol', 'ma', 'ewma', 'gaussian'
        smooth_window = 21      # 平滑窗口大小,None 为自动计算
                               # 常用窗口大小参考:
                               # 5: 超短期信号,对价格变化最敏感,适合日内交易
                               # 7-8: 短期信号,约1.5周期,适合短线交易
                               # 13: 短中期信号,约2-3周期,平衡敏感度和稳定性
                               # 21: 中期信号,约一个月,用于中期趋势判断(默认推荐)
                               # 34: 中长期信号,约1.5个月,增加稳定性
                               # 55: 长期信号,约2.5个月,用于大趋势研判
                               # 89: 超长期信号,约4个月,用于战略性趋势分析
                               # None: 自动计算,取数据长度的15%(但不超过51)
        poly_order = 63            # 多项式阶数(仅用于savgol方法)
        
        # 创建可视化对象
        config = {
            'style': style,
            'use_english': use_english
        }
        visualizer = StockEchartVisualizer(config)
        
        try:
            # 绘制图表
            chart = visualizer.plot_stock_chart(
                ts_code=ts_code,
                display_points=display_points,
                force_refresh=force_refresh,
                long_term=long_term, 
                short_term=short_term,
                save_path=save_path,
                smooth_method=smooth_method,
                smooth_window=smooth_window,
                poly_order=poly_order,
                start_date=start_date,  # 添加起始日期
                #end_date=end_date       # 添加结束日期
            )
            # 如果没有指定保存路径,则在浏览器中显示
            if not save_path:
                temp_path = "temp_chart.html"
                chart.render(temp_path)
                import webbrowser
                webbrowser.open(f"file://{os.path.abspath(temp_path)}")
                print(f"图表已在浏览器中打开,临时文件: {temp_path}")
        except Exception as e:
            print(f"错误: {str(e)}")
            sys.exit(1)






