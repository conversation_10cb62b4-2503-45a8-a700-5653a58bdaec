#nohup /home/<USER>/anaconda3/bin/python -m streamlit run /home/<USER>/jupyter/daily_work/stock_data_analysis/streamlit_stock_analysis.py --server.port=8501 --server.address=0.0.0.0 > /home/<USER>/jupyter/daily_work/stock_data_analysis/streamlit.log 2>&1 &
import streamlit as st
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Grid
import streamlit.components.v1 as components
from cn_indicator_analysis_echarts import plot_stock_chart_echarts
from cn_indicator_analysis_echarts_weekly import plot_stock_chart_echarts_weekly 
from datetime import datetime

# 获取今天的日期并格式化为YYYYMMDD
today_date = datetime.now().strftime("%Y%m%d")

# 设置页面标题和布局
st.set_page_config(
    page_title="股票分析引擎",
    page_icon="📈",
    layout="wide"
)

# 适应不同设备的CSS
st.markdown("""
<style>
    div.stButton > button {
        background-color: #FF5252;
        color: white;
        font-weight: bold;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.3rem;
    }
    div.stButton > button:hover {
        background-color: #FF7F7F;
    }
    
    /* 自定义单选按钮组样式 - 让它看起来像选项卡 */
    div.st-eb {
        padding-top: 0rem;
    }
    div.row-widget.stRadio > div {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        justify-content: space-between;
    }
    div.row-widget.stRadio > div[role="radiogroup"] > label {
        background-color: #f0f2f6;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
        text-align: center;
        min-width: 80px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    div.row-widget.stRadio > div[role="radiogroup"] > label:hover {
        background-color: #e6f0ff;
    }
    div.row-widget.stRadio > div[role="radiogroup"] > label[data-baseweb="radio"] > div:first-child {
        background-color: transparent;
        border: none;
    }
    div.row-widget.stRadio > div[role="radiogroup"] > label[aria-checked="true"] {
        background-color: #4c85e5;
        color: white;
        font-weight: bold;
        border-radius: 5px;
        border-bottom: 2px solid #3666b3;
    }
    div.row-widget.stRadio > div[role="radiogroup"] > label > div:first-child {
        margin-right: 0;
    }
    
    /* 隐藏原始单选按钮的圆圈，只显示文本 */
    div.row-widget.stRadio > div[role="radiogroup"] label > div:first-child {
        display: none;
    }
</style>
""", unsafe_allow_html=True)

# 在侧边栏顶部添加选项卡样式的单选按钮
with st.sidebar:
    st.markdown("<div style='margin-bottom: 15px;'></div>", unsafe_allow_html=True)
    app_mode = st.radio("应用模式选择", ["📈 股票分析", "📁 文件服务"], horizontal=True, label_visibility="collapsed")
    st.markdown("<hr style='margin-top: 15px; margin-bottom: 15px;'>", unsafe_allow_html=True)

# 根据选择的模式显示不同内容
if app_mode == "📈 股票分析":
    # 股票分析内容
    
    # 侧边栏参数设置
    with st.sidebar:
        st.header("参数设置")
        
        # 股票代码输入框（自由填写）
        stock_code = st.text_input("股票代码", value="000001", help="输入6位股票代码")
        
        # 数据频率选择
        data_frequency = st.selectbox("数据频率", options=["日线", "周线"])
        
        # EMA参数（并排放置，添加+/-按钮）
        col3, col4 = st.columns(2)
        with col3:
            st.write("长期EMA周期")
            longterm_period = st.number_input("长期EMA周期输入框", value=26, min_value=5, max_value=60, step=1, label_visibility="collapsed")
        with col4:
            st.write("短期EMA周期")
            shortterm_period = st.number_input("短期EMA周期输入框", value=13, min_value=3, max_value=30, step=1, label_visibility="collapsed")
        
        # 显示点数设置
        st.write("显示点数")
        display_points = st.number_input("显示点数输入框", value=200, min_value=50, max_value=500, step=10, label_visibility="collapsed")
        
        # 平滑方法选择
        smooth_method = st.selectbox("平滑方法", options=["savgol_filter"])
        
        # 平滑参数（并排放置）
        col7, col8 = st.columns(2)
        with col7:
            st.write("平滑窗口大小")
            window_size = st.number_input("平滑窗口大小输入框", value=66, min_value=3, max_value=101, step=2, label_visibility="collapsed")
        with col8:
            st.write("多项式阶数")
            poly_order = st.number_input("多项式阶数输入框", value=1, min_value=0, max_value=5, step=1, label_visibility="collapsed")
        
        # 运行分析按钮
        generate_chart = st.button("运行分析")

    # 主页面内容
    if 'generate_chart' in locals() and generate_chart or 'chart_generated' in st.session_state:
        # 第一次点击或刷新后保持图表状态
        if not 'chart_generated' in st.session_state:
            st.session_state.chart_generated = True

        # 如果用户没有输入有效的股票代码，提示用户
        if not stock_code or len(stock_code.strip()) != 6 or not stock_code.isdigit():
            st.warning("请输入有效的6位股票代码")
        else:
            try:
                # 创建状态容器，并用占位符替代
                status_container = st.empty()
                status_container.info("正在获取股票数据...")
                
                # 创建股票分析图表，传递所有用户自定义参数
                if data_frequency == "日线":
                    chart = plot_stock_chart_echarts(
                        stock_code=stock_code, 
                        display_points=display_points,
                        longterm_period=longterm_period,
                        shortterm_period=shortterm_period,
                        window_size=window_size,
                        smooth_order0=0,  # 使用多项式阶数
                        smooth_order1=poly_order,
                        start_date=None,  # 不限制开始日期
                        end_date=None     # 不限制结束日期
                    )
                else:  # 周线
                    chart = plot_stock_chart_echarts_weekly(
                        stock_code=stock_code, 
                        display_points=display_points,
                        longterm_period=longterm_period,
                        shortterm_period=shortterm_period,
                        window_size=window_size,
                        smooth_order0=0,  # 使用多项式阶数
                        smooth_order1=poly_order,
                        start_date=None,  # 不限制开始日期
                        end_date=None     # 不限制结束日期
                    )
                
                # 将图表渲染为HTML
                html_str = chart.render_embed()
                
                # 添加适应容器的CSS，防止滚动条出现
                html_str = html_str.replace('<head>', '''<head>
<style>
.container {
    width: 100% !important;
    height: 100% !important;
    overflow: hidden !important;
}
.chart-container {
    width: 100% !important;
    height: 100% !important;
    overflow: hidden !important;
}
</style>
''')
                
                # 更新状态
                status_container.success("图表生成完成！")
                
                # 使用components显示生成的HTML，设置scrolling=False以去掉滚动条
                components.html(html_str, height=800, width=None, scrolling=False)
                
            except Exception as e:
                st.error(f"生成图表时出错: {str(e)}")
                if 'chart_generated' in st.session_state:
                    del st.session_state.chart_generated

    else:
        # 显示初始说明
        st.info("请在左侧设置参数，然后点击「运行分析」按钮")
        
        # 示例图像或使用说明
        st.markdown("""
        ### 使用说明
        
        1. 在左侧选择股票代码
        2. 调整所需的技术指标参数
        3. 点击"运行分析"按钮
        4. 等待图表生成后进行分析
        
        本工具使用EMA指标和平滑处理算法进行技术分析，帮助您识别市场趋势和潜在的买卖点。
        
        #### 参数说明
        
        - **长期EMA周期**: 较长周期的指数移动平均线周期，通常为26天
        - **短期EMA周期**: 较短周期的指数移动平均线周期，通常为13天
        - **平滑方法**: 选择用于处理MACD数据的平滑算法
        - **平滑窗口大小**: 平滑算法的窗口大小，影响平滑程度
        - **多项式阶数**: 决定应用Savitzky-Golay滤波器的阶数
        """)

else:  # 文件服务内容
    # 文件服务器内容
    st.markdown("<h1 style='font-size: 1.8rem;'>📁 文件服务器</h1>", unsafe_allow_html=True)
    
    # 嵌入gohttpserver
    gohttpserver_url = "https://file.nulldate.com/"
    
    # 使用HTML组件嵌入iframe
    file_server_iframe = f"""
    <div style="width:100%; height:800px; margin:0; padding:0;">
        <iframe src="{gohttpserver_url}" width="100%" height="100%" frameborder="0" 
            style="border:0; border-radius:4px; box-shadow:0 2px 5px rgba(0,0,0,0.1);"
            allowfullscreen>
        </iframe>
    </div>
    """
    
    # 渲染iframe
    components.html(file_server_iframe, height=800, scrolling=False)
    
    # 添加直接链接选项
    st.markdown(f"""
    如果嵌入视图无法正常工作，请 [点击这里直接访问文件服务器]({gohttpserver_url})
    """)

# 页脚
st.markdown("---")
st.caption("© Stock Analysis Engine - 技术指标分析工具") 