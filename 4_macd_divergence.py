import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text, String
import os
from multiprocessing import Pool
import multiprocessing
from functools import lru_cache
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# 将process_group函数移到类外部
def process_stock_group(group_data):
    """处理单个股票组的数据"""
    code = group_data['code'].iloc[0]
    row = {'code': code}
    
    # 计算最近10天的最小值
    recent_10 = group_data[group_data['rn'] <= 10].copy()
    if not recent_10.empty:
        min_idx = recent_10['macd'].idxmin()
        row.update({
            'diff10_min': recent_10.loc[min_idx, 'macd'],
            'diff10_date': recent_10.loc[min_idx, 'date'],
            'df_10_td': recent_10.loc[min_idx, 'rn']
        })
    
    # 计算各组最小值
    for i, (group_name, group_subset) in enumerate(
        group_data.groupby('group', observed=True), 1
    ):
        if not group_subset.empty:
            min_idx = group_subset['macd'].idxmin()
            row.update({
                f'diff_min_{i}': group_subset.loc[min_idx, 'macd'],
                f'diff{i}_date': group_subset.loc[min_idx, 'date'],
                f'diff{i}_price': group_subset.loc[min_idx, 'close'],
                f'rn_{i}': group_subset.loc[min_idx, 'rn'] if i == 1 else None
            })
    
    return row

# 全局数据库引擎
_DB_ENGINE = None

def get_db_engine():
    """获取数据库引擎（单例模式）"""
    global _DB_ENGINE
    if _DB_ENGINE is None:
        db_url = "mysql+pymysql://root:31490600@10.10.10.2:3306/instockdb"
        _DB_ENGINE = create_engine(
            db_url,
            pool_size=10,  # 优化连接池大小
            max_overflow=20,  # 优化最大溢出连接数
            pool_timeout=30,  # 连接池获取连接的超时时间
            pool_recycle=3600,  # 连接重置时间
            pool_pre_ping=True,  # 连接前检测
            echo=False  # 生产环境关闭SQL日志
        )
    return _DB_ENGINE

class MacdDivergenceAnalyzer:
    def __init__(self, db_url="mysql+pymysql://root:31490600@10.10.10.2:3306/instockdb"):
        self.db_url = db_url
        self.engine = get_db_engine()
        
    def get_base_data(self, days=60):
        """获取基础数据,相当于SQL中的base_date CTE"""
        import time
        start_time = time.time()

        try:
            log_progress(logger, f"开始获取基础数据，查询最近 {days} 天的数据")

            # 首先检查cn_stock_indicators表中是否有ema_diff_smooth66列
            column_check_query = """
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'cn_stock_indicators'
            AND column_name = 'ema_diff_smooth66'
            """
            with self.engine.connect() as conn:
                has_ema_diff_column = conn.execute(text(column_check_query)).fetchone() is not None

            log_progress(logger, f"ema_diff_smooth66列存在: {'是' if has_ema_diff_column else '否'}")
            
            # 构建基础数据查询
            column_list = """
                date, code, name, close, macd, macds, ma26, ma100,
                atr, slope90, atrma5, slope, slope_1, power_slope, r2_slope,
                trix, trix_26, trix_diff, trix_26_diff, ma_26d, diff_26d,
                macd_diff1, macd_diff2
            """
            
            if has_ema_diff_column:
                column_list += ", ema_diff_smooth66, diff0_price"
            
            query = f"""
            WITH RECURSIVE dates AS (
                SELECT MAX(date) as date FROM cn_stock_indicators
                UNION ALL
                SELECT (
                    SELECT MAX(date) 
                    FROM cn_stock_indicators 
                    WHERE date < d.date
                )
                FROM dates d
                WHERE d.date IS NOT NULL
                LIMIT {days}
            ),
            base_data AS (
                SELECT 
                    {column_list},
                    ROW_NUMBER() OVER (PARTITION BY code ORDER BY date DESC) as rn
                FROM cn_stock_indicators
                WHERE date IN (SELECT date FROM dates WHERE date IS NOT NULL)
            )
            SELECT * FROM base_data WHERE rn <= {days}
            ORDER BY code, date DESC
            """

            # 执行查询
            with self.engine.connect() as conn:
                df = pd.read_sql(text(query), conn)

            query_time = time.time() - start_time
            log_progress(logger, f"基础数据查询完成: {len(df)} 条记录，耗时 {query_time:.2f} 秒")

            if df.empty:
                log_warning(logger, "查询结果为空，请检查数据库中是否有数据")
            else:
                unique_codes = df['code'].nunique()
                unique_dates = df['date'].nunique()
                log_progress(logger, f"数据统计: {unique_codes} 只股票，{unique_dates} 个交易日")

            return df

        except Exception as e:
            query_time = time.time() - start_time
            log_error(logger, f"获取基础数据时出错 (耗时 {query_time:.2f} 秒): {str(e)}")
            return pd.DataFrame()

    def calculate_groups(self, df):
        """将数据分组,相当于SQL中的group_number CTE"""
        df['group'] = pd.cut(df['rn'], 
                           bins=[0, 20, 40, 60], 
                           labels=['group1', 'group2', 'group3'],
                           right=True)
        return df

    def calculate_min_values(self, df):
        """使用多进程优化最小值计算"""
        import time
        start_time = time.time()

        if df.empty:
            log_warning(logger, "输入数据为空，跳过最小值计算")
            return pd.DataFrame()

        # 将数据分成多个组
        groups = [group for _, group in df.groupby('code')]
        total_stocks = len(groups)

        log_progress(logger, f"开始计算 {total_stocks} 只股票的MACD最小值")

        # 优化进程数：不超过CPU核心数和股票数量
        optimal_processes = min(multiprocessing.cpu_count(), total_stocks, 8)  # 最多8个进程

        # 使用进程池并行处理
        with Pool(processes=optimal_processes) as pool:
            results = pool.map(process_stock_group, groups)

        calc_time = time.time() - start_time
        result_df = pd.DataFrame(results)

        log_progress(logger, f"MACD最小值计算完成: {len(result_df)} 条记录，耗时 {calc_time:.2f} 秒")
        log_progress(logger, f"处理速度: {total_stocks/calc_time:.1f} 只股票/秒")

        return result_df

    def format_numeric_columns(self, df):
        """格式化数值列以匹配数据库表结构"""
        # double(20,2) 列
        for col in ['close', 'diff1_price', 'diff2_price', 'diff0_price']:
            if col in df.columns:
                df[col] = df[col].round(2)

        # double(22,5) 列
        for col in ['diff_1', 'diff_2', 'ma_26d', 'diff_26d', 'macd_diff1', 
                    'macd_diff2', 'diff10_min', 'ema_diff_smooth66']:
            if col in df.columns:
                df[col] = df[col].round(5)

        # double(20,5) 列
        for col in ['ma100', 'atr', 'slope90', 'atrma5']:
            if col in df.columns:
                df[col] = df[col].round(5)

        # double(20,6) 列
        for col in ['slope', 'slope-1', 'power_slope', 'r2_slope', 
                    'trix', 'trix_26', 'trix_diff', 'trix_26_diff']:
            if col in df.columns:
                df[col] = df[col].round(6)

        return df

    def find_divergence(self, df, min_values):
        """寻找底背离"""
        latest = df[df['rn'] == 1].copy()
        result = pd.merge(latest, min_values, on='code', how='left')
        
        # 计算回调信号
        result['huitiao'] = np.where(
            (result['ma_26d'] > 0) & (result['diff_26d'] < 0),
            1, 0
        )
        
        # 计算底背离信号
        result['jd'] = np.where(
            (result['diff1_price'] < result['diff2_price']) &
            (result['diff_min_1'] > result['diff_min_2']) &
            (result['diff_min_1'] < 0) &
            (result['diff_min_2'] < 0),
            1, 0
        )
        
        # 计算涨跌幅
        def calculate_dmd(row):
            if row['diff1_price'] == 0:
                return '0%'
            return f"{((row['close'] / row['diff1_price'] - 1) * 100):.0f}%"
        
        result['dmd'] = result.apply(calculate_dmd, axis=1)
        
        # 使用rn_1作为d_td
        result['d_td'] = result['rn_1']
        
        return result

    def save_to_history(self, df, analysis_date=None):
        """优化数据库写入操作"""
        import time
        start_time = time.time()

        try:
            if df.empty:
                log_warning(logger, "数据为空，跳过保存操作")
                return

            log_progress(logger, f"开始保存 {len(df)} 条MACD分析数据到历史表")

            # 批量格式化日期列
            date_cols = ['date', 'diff1_date', 'diff2_date', 'diff10_date']
            existing_date_cols = [col for col in date_cols if col in df.columns]
            if existing_date_cols:
                df[existing_date_cols] = df[existing_date_cols].apply(lambda x: pd.to_datetime(x).dt.strftime('%Y%m%d'))

            # 批量格式化数值列
            df = self.format_numeric_columns(df)

            # 批量转换整数列
            int_cols = ['jd', 'huitiao', 'd_td', 'df_10_td']
            existing_int_cols = [col for col in int_cols if col in df.columns]
            if existing_int_cols:
                df[existing_int_cols] = df[existing_int_cols].astype('Int64')
            
            # 检查目标表结构
            with self.engine.connect() as conn:
                # 获取表列信息
                table_columns_query = text("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = 'macd_analy_di_his'
                """)
                table_columns = [row[0] for row in conn.execute(table_columns_query).fetchall()]
                
                # 确保只使用表中存在的列
                df_columns = set(df.columns)
                valid_columns = [col for col in df.columns if col.lower() in [c.lower() for c in table_columns]]
                missing_columns = df_columns - set(valid_columns)
                
                if missing_columns:
                    log_warning(logger, f"以下列在macd_analy_di_his表中不存在，将被忽略: {', '.join(missing_columns)}")
                    df = df[valid_columns]
            
            # 使用事务批量删除和插入
            delete_start = time.time()
            with self.engine.begin() as conn:
                # 获取最新日期并删除记录
                latest_date = df['date'].iloc[0]
                delete_result = conn.execute(
                    text("DELETE FROM macd_analy_di_his WHERE date = :date"),
                    {"date": latest_date}
                )
                delete_time = time.time() - delete_start
                log_progress(logger, f"删除历史数据: {delete_result.rowcount} 条记录，耗时 {delete_time:.2f} 秒")

                # 使用优化的chunksize批量插入
                insert_start = time.time()
                df.to_sql(
                    'macd_analy_di_his',
                    conn,
                    if_exists='append',
                    index=False,
                    method='multi',
                    chunksize=3000,  # 优化chunksize
                    dtype={
                        'date': String(8),
                        'code': String(6),
                        'name': String(20),
                        'dmd': String(18)
                    }
                )
                insert_time = time.time() - insert_start

            total_time = time.time() - start_time
            log_progress(logger, f"数据保存完成: {len(df)} 条记录")
            log_progress(logger, f"性能统计: 插入耗时 {insert_time:.2f}秒, 总耗时 {total_time:.2f}秒")
            log_progress(logger, f"写入速度: {len(df)/total_time:.0f} 条/秒")
            
        except Exception as e:
            log_error(logger, f"保存数据时发生错误: {str(e)}")
            raise

    def process_data(self):
        """处理数据并返回结果"""
        try:
            # 1. 获取基础数据
            df = self.get_base_data()
            if df.empty:
                raise ValueError("未获取到基础数据")
            
            # 获取最新日期
            latest_date = df['date'].max()
            
            # 2. 计算分组
            df = self.calculate_groups(df)
            
            # 3. 计算最小值
            min_values = self.calculate_min_values(df)
            
            # 4. 寻找底背离
            result = self.find_divergence(df, min_values)
            
            # 5. 选择并重命名最终结果列,确保与表结构匹配
            # 检查数据中是否有ema_diff_smooth66列
            has_ema_diff = 'ema_diff_smooth66' in df.columns
            
            # 基础列
            final_columns = {
                'date': 'date',
                'code': 'code',
                'name': 'name',
                'close': 'close',
                'diff_min_1': 'diff_1',
                'diff_min_2': 'diff_2',
                'diff1_date': 'diff1_date',
                'diff2_date': 'diff2_date',
                'diff1_price': 'diff1_price',
                'diff2_price': 'diff2_price',
                'jd': 'jd',
                'd_td': 'd_td',
                'ma_26d': 'ma_26d',
                'diff_26d': 'diff_26d',
                'huitiao': 'huitiao',
                'macd_diff1': 'macd_diff1',
                'macd_diff2': 'macd_diff2',
                'dmd': 'dmd',
                'diff10_min': 'diff10_min',
                'diff10_date': 'diff10_date',
                'df_10_td': 'df_10_td',
                'ma100': 'ma100',
                'atr': 'atr',
                'slope90': 'slope90',
                'atrma5': 'atrma5',
                'slope': 'slope',
                'slope_1': 'slope-1',
                'power_slope': 'power_slope',
                'r2_slope': 'r2_slope',
                'trix': 'trix',
                'trix_26': 'trix_26',
                'trix_diff': 'trix_diff',
                'trix_26_diff': 'trix_26_diff',
                'diff0_price': 'diff0_price'
            }
            
            # 如果有ema_diff_smooth66列，则添加到final_columns中
            if has_ema_diff:
                final_columns['ema_diff_smooth66'] = 'ema_diff_smooth66'
            
            # 检查所有key是否都在result中
            available_keys = [k for k in final_columns.keys() if k in result.columns]
            final_result = result[available_keys].copy()
            final_result.columns = [final_columns[k] for k in available_keys]
            
            # 只保留最新日期的数据
            final_result = final_result[final_result['date'] == latest_date]
            
            # 6. 保存到历史表
            self.save_to_history(final_result)
            
            return final_result
            
        except Exception as e:
            log_error(logger, f"处理数据时发生错误: {str(e)}")
            send_notification(
                message=f"错误信息: {str(e)}",
                title="MACD底背离分析失败",
                tags="MACD分析|错误"
            )
            return None

    @lru_cache(maxsize=128)
    def get_latest_trade_date(self):
        """缓存最新交易日期"""
        with self.engine.connect() as conn:
            result = conn.execute(text("SELECT MAX(date) FROM cn_stock_indicators"))
            return result.scalar()

def main():
    """主函数"""
    import time

    start_time = time.time()
    log_progress(logger, "=== MACD底背离分析开始 ===")

    try:
        analyzer = MacdDivergenceAnalyzer()
        result = analyzer.process_data()

        total_time = time.time() - start_time

        if result is not None:
            divergence_count = len(result[result['jd'] == 1]) if 'jd' in result.columns else 0
            huitiao_count = len(result[result['huitiao'] == 1]) if 'huitiao' in result.columns else 0

            log_progress(logger, "=== MACD底背离分析完成 ===")
            log_progress(logger, f"总计分析: {len(result)} 只股票")
            log_progress(logger, f"发现底背离信号: {divergence_count} 个")
            log_progress(logger, f"发现回调信号: {huitiao_count} 个")
            log_progress(logger, f"总耗时: {total_time:.2f} 秒")
            log_progress(logger, f"平均处理速度: {len(result)/total_time:.1f} 只股票/秒")

            return True
        else:
            log_error(logger, f"MACD分析失败，总耗时: {total_time:.2f} 秒")
            return False

    except Exception as e:
        total_time = time.time() - start_time
        error_msg = f"MACD分析异常 (耗时 {total_time:.2f} 秒): {str(e)}"
        log_error(logger, error_msg)

        send_notification(
            message=error_msg,
            title="MACD底背离分析异常",
            tags="MACD分析|异常"
        )
        return False
if __name__ == "__main__":
    import sys

    success = main()
    if not success:
        sys.exit(1)