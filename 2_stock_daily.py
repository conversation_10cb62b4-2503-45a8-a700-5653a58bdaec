import pandas as pd
import tushare as ts
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv
from contextlib import contextmanager
from sqlalchemy.pool import QueuePool
import warnings
import pathlib
from serverchan_sdk import sc_send
from urllib3.exceptions import ConnectTimeoutError
from requests.exceptions import RequestException
# 先导入日志配置
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification
# 再导入交易日历模块
from trade_calendar import is_trade_day, get_latest_trade_day, get_next_trade_day

# 屏蔽警告信息
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 初始化环境变量
load_dotenv()

# 创建日志目录
log_dir = pathlib.Path(__file__).parent / 'log'
log_dir.mkdir(exist_ok=True)

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# 数据库连接配置
DB_CONFIG = {
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', '31490600'),
    'host': os.getenv('DB_HOST', '**********'),
    'port': os.getenv('DB_PORT', '3306'),
    'database': os.getenv('DB_NAME', 'instockdb'),
    'charset': 'utf8mb4'
}

TUSHARE_TOKEN = os.getenv('TUSHARE_TOKEN', '5aba669a485730beded0d604bd8ccfdaca39f8c9c09ef2d0ed78ed77')

# API访问控制
ENABLE_RATE_LIMIT = True  # 是否启用API访问频率限制
API_RATE_LIMIT = 800  # 每分钟最大请求次数
API_RATE_INTERVAL = 60  # 时间窗口(秒)

class RateLimiter:
    """API调用限速器"""
    _last_call = 0
    
    def __init__(self, limit, interval):
        """初始化限速器"""
        self.limit = limit  # 时间窗口内最大请求数
        self.interval = interval  # 时间窗口大小(秒)
        self.requests = []  # 请求时间队列
        self.cooldown_time = 0  # 冷却期(秒)
        self.last_reset = 0  # 上次重置时间
    
    def wait_if_needed(self):
        """等待以满足速率限制"""
        now = time.time()

        # 如果在冷却期，则等待冷却结束
        if now - self.last_reset < self.cooldown_time:
            wait_time = self.cooldown_time - (now - self.last_reset)
            if wait_time > 0:
                time.sleep(wait_time)
            self.last_reset = time.time()  # 重置
            self.requests = []  # 清空请求队列
            return

        # 优化：批量移除过期请求
        cutoff_time = now - self.interval
        self.requests = [req_time for req_time in self.requests if req_time > cutoff_time]

        # 判断是否需要等待
        if len(self.requests) >= self.limit:
            # 等待最早的请求过期
            sleep_time = self.interval - (now - self.requests[0])
            if sleep_time > 0:
                time.sleep(sleep_time)
                # 重新清理过期请求
                now = time.time()
                cutoff_time = now - self.interval
                self.requests = [req_time for req_time in self.requests if req_time > cutoff_time]

        self.requests.append(now)
    
    def trigger_cooldown(self, duration=60):
        """触发冷却期"""
        self.cooldown_time = duration
        self.last_reset = time.time()
        self.requests = []
        log_progress(logger, f"触发API冷却期 {duration} 秒")

# 创建限速器实例
rate_limiter = RateLimiter(API_RATE_LIMIT, API_RATE_INTERVAL)

# 数据库连接池配置
DB_POOL_SIZE = 10
DB_MAX_OVERFLOW = 20
DB_POOL_TIMEOUT = 30

# 表结构定义
TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS `ts_stock_daily` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `ts_code` VARCHAR(10) NOT NULL COMMENT 'TS代码',
    `stock_code` VARCHAR(6) NOT NULL COMMENT '股票代码',
    `name` VARCHAR(50) NOT NULL COMMENT '股票名称',
    `trade_date` DATE NOT NULL COMMENT '交易日期',
    `open` DECIMAL(10,4) COMMENT '开盘价(前复权)',
    `high` DECIMAL(10,4) COMMENT '最高价(前复权)',
    `low` DECIMAL(10,4) COMMENT '最低价(前复权)',
    `close` DECIMAL(10,4) COMMENT '收盘价(前复权)',
    `pre_close` DECIMAL(10,4) COMMENT '昨收价',
    `change` DECIMAL(10,4) COMMENT '涨跌额',
    `pct_chg` DECIMAL(10,4) COMMENT '涨跌幅',
    `vol` DECIMAL(20,4) COMMENT '成交量(手)',
    `amount` DECIMAL(20,4) COMMENT '成交额(千元)',
    `adj_factor` DECIMAL(10,4) COMMENT '复权因子',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_ts_code_trade_date` (`ts_code`, `trade_date`),
    KEY `idx_stock_code` (`stock_code`),
    KEY `idx_trade_date` (`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票日线行情表(前复权)';
"""

# 全局数据库引擎
_DB_ENGINE = None

def get_db_engine():
    """获取数据库引擎（单例模式）"""
    global _DB_ENGINE
    if _DB_ENGINE is None:
        _DB_ENGINE = create_engine(
            f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
            f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}",
            poolclass=QueuePool,
            pool_size=DB_POOL_SIZE,
            max_overflow=DB_MAX_OVERFLOW,
            pool_timeout=DB_POOL_TIMEOUT,
            pool_pre_ping=True,
            pool_recycle=3600,  # 连接回收时间
            echo=False  # 生产环境关闭SQL日志
        )
    return _DB_ENGINE

@contextmanager
def db_connection():
    """数据库连接上下文管理"""
    engine = get_db_engine()
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except SQLAlchemyError as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def init_database():
    """初始化数据库表结构"""
    try:
        with db_connection() as conn:
            conn.execute(text(TABLE_SCHEMA))
            log_progress(logger, "数据库表初始化完成")
    except Exception as e:
        log_error(logger, f"数据库初始化失败: {str(e)}")
        raise

def retry_api_call(func, *args, max_retries=5, initial_delay=30, **kwargs):
    """通用API调用重试函数"""
    retry_count = 0
    max_wait_time = 300  # 最大等待时间5分钟

    while True:  # 无限重试，直到成功
        try:
            if ENABLE_RATE_LIMIT:
                rate_limiter.wait_if_needed()
            return func(*args, **kwargs)

        except (ConnectTimeoutError, RequestException) as e:
            retry_count += 1
            wait_time = min(initial_delay * (2 ** (retry_count - 1)), max_wait_time)

            if retry_count >= max_retries:
                wait_time = max_wait_time
                retry_count = 0  # 重置重试计数

            log_progress(logger, f"连接超时, 第{retry_count}次重试, 等待{wait_time}秒")
            time.sleep(wait_time)

        except Exception as e:
            error_msg = str(e)

            # 处理API限制
            if "每分钟最多访问该接口" in error_msg or "接口限制" in error_msg:
                rate_limiter.trigger_cooldown(90)  # 触发90秒冷却期
                continue

            retry_count += 1
            wait_time = min(initial_delay * (2 ** (retry_count - 1)), max_wait_time)

            if retry_count >= max_retries:
                wait_time = max_wait_time
                retry_count = 0

            log_error(logger, f"API调用失败: {error_msg}, 等待{wait_time}秒后重试")
            time.sleep(wait_time)

def get_trade_calendar(pro, start_date, end_date):
    """获取交易日历"""
    def _get_calendar():
        df = pro.trade_cal(exchange='', start_date=start_date, end_date=end_date)
        if df is None or df.empty:
            raise Exception("获取交易日历返回空数据")
        return df[df['is_open'] == 1]['cal_date'].tolist()
    
    return retry_api_call(_get_calendar)

def validate_daily_data(df):
    """数据校验与清洗"""
    if df.empty:
        return df

    # 创建数据框的副本
    df = df.copy()

    try:
        # 转换日期格式
        df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d').dt.date

        # 批量数值类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'pre_close',
                          'change', 'pct_chg', 'vol', 'amount']

        existing_numeric_cols = [col for col in numeric_columns if col in df.columns]
        if existing_numeric_cols:
            df[existing_numeric_cols] = df[existing_numeric_cols].apply(pd.to_numeric, errors='coerce')

        # 删除重复项
        df = df.drop_duplicates(subset=['ts_code', 'trade_date'], keep='first')

        # 过滤无效数据
        valid_df = df.dropna(subset=['ts_code', 'stock_code', 'name', 'trade_date'])

        if len(valid_df) < len(df):
            log_progress(logger, f"数据清洗：从 {len(df)} 条减少到 {len(valid_df)} 条有效记录")

        return valid_df

    except Exception as e:
        log_error(logger, f"数据验证失败: {str(e)}")
        raise

def get_adj_factors(pro, trade_dates):
    """按日期批量获取复权因子"""
    all_factors = []
    failed_dates = []
    
    for date in trade_dates:
        try:
            def _get_factor():
                df_factor = pro.adj_factor(trade_date=date, timeout=60)
                if df_factor is None:
                    raise Exception("获取复权因子返回空数据")
                return df_factor
            
            df = retry_api_call(_get_factor)
            if not df.empty:
                all_factors.append(df)
                log_progress(logger, f"成功获取 {date} 的复权因子数据")
            
        except Exception as e:
            log_error(logger, f"获取{date}复权因子失败: {str(e)}")
            failed_dates.append(date)
    
    # 重试失败的日期
    while failed_dates:
        date = failed_dates[0]
        try:
            def _get_factor():
                df_factor = pro.adj_factor(trade_date=date, timeout=60)
                if df_factor is None:
                    raise Exception("获取复权因子返回空数据")
                return df_factor
            
            df = retry_api_call(_get_factor)
            if not df.empty:
                all_factors.append(df)
                failed_dates.remove(date)
                log_progress(logger, f"重试成功获取 {date} 的复权因子数据，还剩 {len(failed_dates)} 个日期待重试")
            
        except Exception as e:
            # 将失败的日期移到列表末尾
            failed_dates.remove(date)
            failed_dates.append(date)
            log_error(logger, f"重试获取{date}复权因子失败: {str(e)}")
            if len(failed_dates) == 1:
                time.sleep(300)  # 如果只剩最后一个日期，增加等待时间
    
    # 合并所有日期的复权因子数据
    if all_factors:
        return pd.concat(all_factors, ignore_index=True)
    return pd.DataFrame()

def get_daily_data(pro, trade_dates):
    """按日期批量获取日线数据"""
    all_daily = []
    failed_dates = []
    
    for date in trade_dates:
        try:
            def _get_daily():
                df_daily = pro.daily(trade_date=date, timeout=60)
                if df_daily is None:
                    raise Exception("获取日线数据返回空数据")
                return df_daily
            
            df = retry_api_call(_get_daily)
            if not df.empty:
                all_daily.append(df)
                log_progress(logger, f"成功获取 {date} 的日线数据")
            
        except Exception as e:
            log_error(logger, f"获取{date}日线数据失败: {str(e)}")
            failed_dates.append(date)
    
    # 重试失败的日期
    while failed_dates:
        date = failed_dates[0]
        try:
            def _get_daily():
                df_daily = pro.daily(trade_date=date, timeout=60)
                if df_daily is None:
                    raise Exception("获取日线数据返回空数据")
                return df_daily
            
            df = retry_api_call(_get_daily)
            if not df.empty:
                all_daily.append(df)
                failed_dates.remove(date)
                log_progress(logger, f"重试成功获取 {date} 的日线数据，还剩 {len(failed_dates)} 个日期待重试")
            
        except Exception as e:
            # 将失败的日期移到列表末尾
            failed_dates.remove(date)
            failed_dates.append(date)
            log_error(logger, f"重试获取{date}日线数据失败: {str(e)}")
            if len(failed_dates) == 1:
                time.sleep(300)  # 如果只剩最后一个日期，增加等待时间
    
    # 合并所有日期的日线数据
    if all_daily:
        return pd.concat(all_daily, ignore_index=True)
    return pd.DataFrame()

# 初始化tushare接口
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api(TUSHARE_TOKEN)

def get_and_save_daily_data():
    """获取和保存股票日线数据"""
    try:
        # 清空数据表
        truncate_table()
        
        # 获取所有上市公司列表
        stocks = pro.stock_basic(exchange='', list_status='L', 
                               fields='ts_code,symbol,name')
        
        # 计算日期范围(近2年)
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=730)).strftime('%Y%m%d')
        
        # 获取交易日历
        trade_dates = get_trade_calendar(pro, start_date, end_date)
        trade_dates.sort()  # 按时间顺序排列
        
        log_progress(logger, f"开始获取日线数据,交易日数量: {len(trade_dates)}")
        
        # 1. 获取所有交易日的日线数据
        log_progress(logger, "开始获取日线数据...")
        df_daily = get_daily_data(pro, trade_dates)
        if df_daily.empty:
            raise Exception("未获取到日线数据")
        log_progress(logger, f"日线数据获取完成，共 {len(df_daily)} 条记录")
        
        # 2. 获取所有交易日的复权因子
        log_progress(logger, "开始获取复权因子...")
        df_factor = get_adj_factors(pro, trade_dates)
        if df_factor.empty:
            raise Exception("未获取到复权因子数据")
        log_progress(logger, f"复权因子获取完成，共 {len(df_factor)} 条记录")
        
        # 3. 合并数据
        log_progress(logger, "开始合并数据...")
        df = pd.merge(df_daily, df_factor, on=['ts_code', 'trade_date'], how='left')
        df['adj_factor'] = df['adj_factor'].fillna(1.0)
        
        # 4. 获取每个股票最新的复权因子
        log_progress(logger, "开始计算最新复权因子...")
        latest_factors = df.groupby('ts_code')['adj_factor'].last().to_dict()
        
        # 5. 批量计算前复权价格
        log_progress(logger, "开始计算前复权价格...")
        price_cols = ['open', 'high', 'low', 'close']
        for ts_code, latest_factor in latest_factors.items():
            mask = df['ts_code'] == ts_code
            for col in price_cols:
                df.loc[mask, col] = df.loc[mask, col] * df.loc[mask, 'adj_factor'] / latest_factor
        
        # 6. 添加股票代码和名称
        log_progress(logger, "开始添加股票信息...")
        stock_info = stocks.set_index('ts_code')[['symbol', 'name']]
        df = df.join(stock_info, on='ts_code')
        df = df.rename(columns={'symbol': 'stock_code'})
        
        # 7. 数据清洗和保存
        log_progress(logger, "开始数据清洗...")
        clean_df = validate_daily_data(df)
        
        # 只保留需要的列
        columns_to_save = ['ts_code', 'stock_code', 'name', 'trade_date', 
                          'open', 'high', 'low', 'close', 'pre_close',
                          'change', 'pct_chg', 'vol', 'amount', 'adj_factor']
        clean_df = clean_df[columns_to_save]
        
        # 8. 保存数据
        log_progress(logger, "开始保存数据...")
        save_daily_data(clean_df)
        
        log_progress(logger, f"所有数据处理完成，共处理 {len(clean_df)} 条记录")
        
    except Exception as e:
        log_error(logger, f"数据获取过程发生错误: {str(e)}")
        raise

def truncate_table():
    """清空数据表"""
    try:
        with db_connection() as conn:
            conn.execute(text("TRUNCATE TABLE ts_stock_daily"))
            log_progress(logger, "数据表已清空")
    except Exception as e:
        log_error(logger, f"清空数据表失败: {str(e)}")
        raise

def save_daily_data(df):
    """保存数据到数据库"""
    import time

    if df.empty:
        log_progress(logger, "无数据需要保存")
        return

    start_time = time.time()
    max_retries = 3
    batch_size = 3000  # 优化批次大小
    total_records = len(df)
    processed_records = 0

    log_progress(logger, f"开始保存数据到数据库，总记录数: {total_records:,}，批次大小: {batch_size:,}")

    # 按批次保存数据
    total_batches = (total_records + batch_size - 1) // batch_size

    for i in range(0, total_records, batch_size):
        batch_start_time = time.time()
        batch_df = df.iloc[i:i+batch_size]
        batch_num = i // batch_size + 1
        retry_count = 0

        while retry_count < max_retries:
            try:
                with db_connection() as conn:
                    batch_df.to_sql(
                        'ts_stock_daily',
                        conn,
                        if_exists='append',
                        index=False,
                        method='multi',
                        chunksize=1000  # 内部分块大小
                    )

                    processed_records += len(batch_df)
                    batch_time = time.time() - batch_start_time

                    # 进度报告
                    if batch_num % 5 == 0 or processed_records == total_records:
                        progress_pct = processed_records / total_records * 100
                        elapsed_time = time.time() - start_time
                        avg_speed = processed_records / elapsed_time if elapsed_time > 0 else 0

                        log_progress(logger,
                            f"批次 {batch_num}/{total_batches}: {processed_records:,}/{total_records:,} "
                            f"({progress_pct:.1f}%) - 批次耗时: {batch_time:.2f}s, 平均速度: {avg_speed:.0f}条/秒")

                    break  # 保存成功，跳出重试循环

            except SQLAlchemyError as e:
                retry_count += 1
                if retry_count < max_retries:
                    log_progress(logger, f"批次 {batch_num} 写入失败，第 {retry_count} 次重试: {str(e)}")
                    time.sleep(retry_count)  # 递增等待时间
                else:
                    log_error(logger, f"批次 {batch_num} 写入失败，已重试 {max_retries} 次: {str(e)}")
                    raise

    total_time = time.time() - start_time
    avg_speed = processed_records / total_time if total_time > 0 else 0
    log_progress(logger, f"数据保存完成: {processed_records:,} 条记录，总耗时: {total_time:.2f}秒，平均速度: {avg_speed:.0f}条/秒")

def main():
    """主函数"""
    import time

    start_time = time.time()
    log_progress(logger, "=== 股票日线数据同步开始 ===")

    try:
        # 初始化数据库
        init_start = time.time()
        init_database()
        init_time = time.time() - init_start
        log_progress(logger, f"数据库初始化完成，耗时 {init_time:.2f} 秒")

        # 获取和保存日线数据
        data_start = time.time()
        get_and_save_daily_data()
        data_time = time.time() - data_start

        # 总结
        total_time = time.time() - start_time
        log_progress(logger, f"=== 股票日线数据同步完成 ===")
        log_progress(logger, f"数据处理耗时: {data_time:.2f} 秒，总耗时: {total_time:.2f} 秒")

        return True

    except Exception as e:
        total_time = time.time() - start_time
        error_msg = str(e)
        log_error(logger, f"程序执行异常 (耗时 {total_time:.2f} 秒): {error_msg}")

        send_notification(
            message=f"日线数据同步失败: {error_msg}",
            title="日线数据同步失败",
            tags="日线数据|失败"
        )
        return False

if __name__ == '__main__':
    import sys

    success = main()
    if not success:
        sys.exit(1)