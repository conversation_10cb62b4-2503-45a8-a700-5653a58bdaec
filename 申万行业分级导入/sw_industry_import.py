import pandas as pd
import pymysql
from sqlalchemy import create_engine
import os

# 数据库连接配置
DB_CONFIG = {
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', '31490600'),
    'host': os.getenv('DB_HOST', '**********'),
    'port': int(os.getenv('DB_PORT', '3306')),
    'database': os.getenv('DB_NAME', 'instockdb'),
    'charset': 'utf8mb4'
}

def create_sw_industry_table(conn):
    """创建申万行业分类表"""
    cursor = conn.cursor()
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS sw_industry (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ts_code VARCHAR(10) COMMENT '股票代码',
        name VARCHAR(50) COMMENT '股票简称',
        l1_code VARCHAR(10) COMMENT '一级行业代码',
        l1_name VARCHAR(50) COMMENT '一级行业名称',
        l2_code VARCHAR(10) COMMENT '二级行业代码',
        l2_name VARCHAR(50) COMMENT '二级行业名称',
        l3_code VARCHAR(10) COMMENT '三级行业代码',
        l3_name VARCHAR(50) COMMENT '三级行业名称',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='申万行业分类表';
    """
    cursor.execute(create_table_sql)
    conn.commit()
    cursor.close()

def import_excel_data(excel_path):
    """导入Excel数据到数据库"""
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        
        # 重命名列以匹配数据库字段
        df_renamed = df.rename(columns={
            'ts_code(股票代码)': 'ts_code',
            'name(股票简称)': 'name',
            'l1_code(一级行业名称)': 'l1_name',
            'l2_code(二级行业名称)': 'l2_name',
            'l3_code(三级行业名称)': 'l3_name'
        })
        
        # 创建数据库连接
        engine = create_engine(
            f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
            f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"
        )
        
        # 连接数据库创建表
        conn = pymysql.connect(**DB_CONFIG)
        create_sw_industry_table(conn)
        conn.close()
        
        # 使用 pandas 的 to_sql 方法导入数据
        # 如果数据已存在，则替换
        df_renamed.to_sql('sw_industry', engine, if_exists='replace', index=False)
        
        print(f"成功导入 {len(df_renamed)} 条数据到申万行业分类表")
        
    except Exception as e:
        print(f"导入数据时发生错误: {str(e)}")
    finally:
        if 'engine' in locals():
            engine.dispose()

if __name__ == "__main__":
    # Excel文件路径
    excel_path = "/Users/<USER>/Documents/git/stock-analysis-engine/申万行业分级导入/申万行业分类(剔除退市和ST).xlsx"  # 请修改为实际的Excel文件路径
    import_excel_data(excel_path) 