"""
使用adata API获取前复权日线数据并入库
Created on 2025-08-17
@author: stock-analysis-engine
"""
import pandas as pd
import adata
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv
from contextlib import contextmanager
from sqlalchemy.pool import QueuePool
import warnings
import pathlib
from serverchan_sdk import sc_send
from urllib3.exceptions import ConnectTimeoutError
from requests.exceptions import RequestException
import concurrent.futures
import threading
import queue
import random

# 导入新的代理管理系统
from proxy_config import DEFAULT_CONFIG
from proxy_manager import get_proxy_manager
from proxy_health import get_health_monitor
from proxy_blacklist import get_blacklist_detector
from proxy_retry import get_retry_handler, RetryStrategy

# 先导入日志配置
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification
# 再导入交易日历模块
from trade_calendar import is_trade_day, get_latest_trade_day, get_next_trade_day

# 屏蔽警告信息
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 初始化环境变量
load_dotenv()

# 创建日志目录
log_dir = pathlib.Path(__file__).parent / 'log'
log_dir.mkdir(exist_ok=True)

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# 数据库连接配置
DB_CONFIG = {
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', '31490600'),
    'host': os.getenv('DB_HOST', '**********'),
    'port': os.getenv('DB_PORT', '3306'),
    'database': os.getenv('DB_NAME', 'instockdb'),
    'charset': 'utf8mb4'
}

# 数据更新模式配置
INCREMENTAL_MODE = True  # 默认使用增量更新模式
FULL_HISTORICAL_MODE = False  # 完整历史数据同步模式
INCREMENTAL_DAYS_BACK = 2  # 增量模式回溯天数（默认更新最近5个交易日）
ENABLE_TRANSACTION_SAFETY = True  # 启用事务安全保护

# 并发处理配置（优化后）
ENABLE_RATE_LIMIT = False  # 禁用API频率限制，使用并发处理
CONCURRENT_WORKERS = 10  # 优化：减少并发工作线程数，提高稳定性
CONCURRENT_PROXY_COUNT = 10  # 优化：减少并发代理数量，匹配工作线程数
STOCKS_PER_WORKER = 200  # 优化：减少每个工作线程处理的股票数量
WORKER_DELAY = 0.5  # 优化：增加工作线程延迟，避免过于频繁的请求

# 代理轮换配置
PROXY_ROTATION_INTERVAL = 50  # 每处理50个股票轮换一次代理IP
ENABLE_WORKER_PROXY_ISOLATION = True  # 启用工作线程代理隔离

# 重试机制配置
MAX_RETRY_ATTEMPTS = 3  # 每个股票最大重试次数
RETRY_DELAY = 2.0  # 重试间隔（秒）

# 初始化新的代理管理系统
proxy_config = DEFAULT_CONFIG
proxy_manager = get_proxy_manager(proxy_config)
health_monitor = get_health_monitor(proxy_config)
blacklist_detector = get_blacklist_detector(proxy_config)
retry_handler = get_retry_handler(proxy_config)

# 保持向后兼容的配置变量
USE_PROXY = proxy_config.enable_proxy


# 数据库连接池配置
DB_POOL_SIZE = 10
DB_MAX_OVERFLOW = 20
DB_POOL_TIMEOUT = 30

# 表结构定义
TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS `ts_stock_daily` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `ts_code` VARCHAR(10) NOT NULL COMMENT 'TS代码',
    `stock_code` VARCHAR(6) NOT NULL COMMENT '股票代码',
    `name` VARCHAR(50) NOT NULL COMMENT '股票名称',
    `trade_date` DATE NOT NULL COMMENT '交易日期',
    `open` DECIMAL(10,4) COMMENT '开盘价(前复权)',
    `high` DECIMAL(10,4) COMMENT '最高价(前复权)',
    `low` DECIMAL(10,4) COMMENT '最低价(前复权)',
    `close` DECIMAL(10,4) COMMENT '收盘价(前复权)',
    `pre_close` DECIMAL(10,4) COMMENT '昨收价',
    `change` DECIMAL(10,4) COMMENT '涨跌额',
    `pct_chg` DECIMAL(10,4) COMMENT '涨跌幅',
    `vol` DECIMAL(20,4) COMMENT '成交量(手)',
    `amount` DECIMAL(20,4) COMMENT '成交额(千元)',
    `adj_factor` DECIMAL(10,4) COMMENT '复权因子',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_ts_code_trade_date` (`ts_code`, `trade_date`),
    KEY `idx_stock_code` (`stock_code`),
    KEY `idx_trade_date` (`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票日线行情表(前复权)';
"""

# 全局数据库引擎
_DB_ENGINE = None

def get_db_engine():
    """获取数据库引擎（单例模式）"""
    global _DB_ENGINE
    if _DB_ENGINE is None:
        _DB_ENGINE = create_engine(
            f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
            f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}",
            poolclass=QueuePool,
            pool_size=DB_POOL_SIZE,
            max_overflow=DB_MAX_OVERFLOW,
            pool_timeout=DB_POOL_TIMEOUT,
            pool_pre_ping=True,
            pool_recycle=3600,  # 连接回收时间
            echo=False  # 生产环境关闭SQL日志
        )
    return _DB_ENGINE

@contextmanager
def db_connection():
    """数据库连接上下文管理"""
    engine = get_db_engine()
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except SQLAlchemyError as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def init_database():
    """初始化数据库表结构"""
    try:
        with db_connection() as conn:
            conn.execute(text(TABLE_SCHEMA))
            log_progress(logger, "数据库表初始化完成")
    except Exception as e:
        log_error(logger, f"数据库初始化失败: {str(e)}")
        raise

def retry_api_call(func, *args, max_retries=5, initial_delay=30, **kwargs):
    """通用API调用重试函数 - 使用新的智能重试系统"""
    try:
        # 使用新的重试处理器，但保持无限重试的行为
        return retry_handler.execute_with_retry(
            func, *args,
            max_retries=max_retries if max_retries < 100 else 100,  # 限制最大重试次数避免真正的无限循环
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            use_proxy=USE_PROXY,
            context_name=func.__name__ if hasattr(func, '__name__') else 'api_call',
            **kwargs
        )
    except Exception as e:
        # 对于无限重试模式，继续重试而不是返回None
        log_error(logger, f"API调用失败，将继续重试: {str(e)}")
        time.sleep(30)  # 等待30秒后重试
        return retry_api_call(func, *args, max_retries=max_retries, initial_delay=initial_delay, **kwargs)

# 向后兼容的代理函数 - 现在使用新的代理管理系统
def get_new_proxy():
    """获取新的代理服务器并添加到代理池 (向后兼容)"""
    try:
        proxy = proxy_manager.get_proxy()
        return proxy is not None
    except Exception as e:
        print(f"获取新代理失败: {e}")
        return False

def is_proxy_expired(proxy):
    """检查代理是否已过期 (向后兼容)"""
    # 新系统内部管理过期，这里总是返回False以保持兼容性
    return False

def setup_proxy():
    """初始化代理池 (向后兼容)"""
    if not USE_PROXY:
        return True

    try:
        proxy = proxy_manager.get_proxy()
        return proxy is not None
    except Exception:
        return False

def get_proxy_from_pool():
    """从代理池获取一个可用的代理 (向后兼容)"""
    if not USE_PROXY:
        return None

    try:
        return proxy_manager.get_proxy()
    except Exception as e:
        print(f"从代理池获取代理失败: {e}")
        return None

def remove_proxy_from_pool(proxy):
    """从代理池中移除代理 (向后兼容)"""
    if not USE_PROXY or not proxy:
        return

    try:
        proxy_manager.blacklist_proxy(proxy, "手动移除")
        print(f"代理 {proxy} 已从池中移除")
    except Exception as e:
        print(f"移除代理失败: {e}")

def get_stock_codes_from_db():
    """从数据库获取所有股票代码"""
    try:
        with db_connection() as conn:
            query = text("SELECT stock_code, name FROM stock_basic")
            result = conn.execute(query)
            stock_codes = [(row[0], row[1]) for row in result.fetchall()]
            log_progress(logger, f"从数据库获取到 {len(stock_codes)} 只股票代码")
            return stock_codes
    except Exception as e:
        log_error(logger, f"获取股票代码失败: {str(e)}")
        raise


def get_recent_trading_days(days_back=5):
    """获取最近N个交易日"""
    try:
        trading_days = []
        current_date = datetime.now()

        # 向前查找交易日
        check_date = current_date
        while len(trading_days) < days_back and check_date >= current_date - timedelta(days=days_back * 2):
            if is_trade_day(check_date):
                trading_days.append(check_date.date())
            check_date -= timedelta(days=1)

        trading_days.reverse()  # 按时间顺序排列
        log_progress(logger, f"获取到最近 {len(trading_days)} 个交易日: {[str(d) for d in trading_days]}")
        return trading_days

    except Exception as e:
        log_error(logger, f"获取最近交易日失败: {str(e)}")
        raise


def get_date_range_for_mode():
    """根据模式获取日期范围"""
    try:
        if INCREMENTAL_MODE:
            log_progress(logger, "=== 增量更新模式 ===")

            # 获取最近的交易日
            recent_days = get_recent_trading_days(INCREMENTAL_DAYS_BACK)
            if not recent_days:
                raise Exception("未找到最近的交易日")

            start_date = recent_days[0].strftime('%Y-%m-%d')
            end_date = recent_days[-1].strftime('%Y-%m-%d')

            log_progress(logger, f"增量更新日期范围: {start_date} 至 {end_date}")
            log_progress(logger, f"将更新最近 {len(recent_days)} 个交易日的数据")

            return start_date, end_date, recent_days
        else:
            log_progress(logger, "=== 完整历史数据同步模式 ===")
            # 完整历史模式（现有逻辑）
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=730)).strftime('%Y-%m-%d')

            log_progress(logger, f"历史数据范围: {start_date} 至 {end_date}")

            return start_date, end_date, None

    except Exception as e:
        log_error(logger, f"获取日期范围失败: {str(e)}")
        raise


def check_existing_daily_data(target_dates):
    """检查指定日期范围是否已有日线数据"""
    try:
        if not target_dates:
            return False, 0

        with db_connection() as conn:
            # 构建日期列表的查询条件
            date_list = "', '".join([str(d) for d in target_dates])
            query = text(f"SELECT COUNT(*) FROM ts_stock_daily WHERE trade_date IN ('{date_list}')")
            result = conn.execute(query)
            count = result.scalar()

            log_progress(logger, f"检查现有数据: {len(target_dates)} 个目标日期已有 {count} 条记录")
            return count > 0, count

    except Exception as e:
        log_error(logger, f"检查现有数据失败: {str(e)}")
        raise


def delete_daily_data_by_dates(conn, target_dates):
    """删除指定日期范围的日线数据（在事务中执行）"""
    try:
        if not target_dates:
            return 0

        # 构建日期列表的查询条件
        date_list = "', '".join([str(d) for d in target_dates])

        # 先查询要删除的记录数
        count_query = text(f"SELECT COUNT(*) FROM ts_stock_daily WHERE trade_date IN ('{date_list}')")
        count_result = conn.execute(count_query)
        delete_count = count_result.scalar()

        if delete_count > 0:
            log_progress(logger, f"⚠️  准备删除 {len(target_dates)} 个日期的 {delete_count} 条现有记录")

            # 执行删除操作
            delete_query = text(f"DELETE FROM ts_stock_daily WHERE trade_date IN ('{date_list}')")
            result = conn.execute(delete_query)

            log_progress(logger, f"✅ 成功删除 {len(target_dates)} 个日期的 {result.rowcount} 条记录")
            return result.rowcount
        else:
            log_progress(logger, f"目标日期范围无现有数据，无需删除")
            return 0

    except Exception as e:
        log_error(logger, f"删除指定日期数据失败: {str(e)}")
        raise

def get_stock_daily_data_adata(stock_code, start_date=None, end_date=None):
    """使用adata API获取单只股票的前复权日线数据（注意：每次调用都会获取新代理，建议使用工作线程专用代理版本）"""
    try:
        def _get_data():
            start_time = time.time()
            current_proxy = None

            try:
                # 从新的代理管理器获取代理（警告：这里每次都会获取新代理）
                if USE_PROXY:
                    current_proxy = proxy_manager.get_proxy()
                    if current_proxy:
                        adata.proxy(is_proxy=True, ip=current_proxy)

                # 调用adata API获取前复权日线数据
                result = adata.stock.market.get_market(
                    stock_code=stock_code,
                    start_date=start_date,
                    end_date=end_date,
                    k_type=1,  # 日K
                    adjust_type=1  # 前复权
                )

                if result is None or result.empty:
                    raise Exception(f"获取股票 {stock_code} 的日线数据返回空数据")

                # 记录成功
                response_time = time.time() - start_time
                if current_proxy:
                    proxy_manager.report_success(current_proxy, response_time)
                    health_monitor.record_request(current_proxy, True, response_time)

                return result

            except Exception as e:
                # 记录失败并分析是否需要拉黑代理
                response_time = time.time() - start_time
                error_msg = str(e)

                if current_proxy:
                    # 分析是否需要拉黑代理
                    should_blacklist, blacklist_reason, details = blacklist_detector.analyze_response(
                        current_proxy, exception=e
                    )

                    if should_blacklist:
                        blacklist_detector.record_failure(current_proxy, blacklist_reason, details)
                        proxy_manager.blacklist_proxy(current_proxy, details)
                    else:
                        proxy_manager.report_failure(current_proxy, error_msg)
                        health_monitor.record_request(current_proxy, False, response_time, "api_error", error_msg)

                raise e
        
        df = retry_api_call(_get_data)
        return df
    
    except Exception as e:
        log_error(logger, f"获取股票 {stock_code} 日线数据失败: {str(e)}")
        # 优化：更智能的代理问题判断
        error_msg = str(e).lower()
        if USE_PROXY and any(keyword in error_msg for keyword in [
            "接口限制", "rate limit", "too many requests", "forbidden",
            "unauthorized", "proxy", "timeout", "connection"
        ]):
            log_progress(logger, f"检测到代理相关错误，尝试获取新代理: {str(e)}")
            get_new_proxy()  # 获取新代理
        elif "返回空数据" in str(e):
            # 对于空数据，不立即换代理，可能是正常情况（如停牌股票、新股等）
            log_progress(logger, f"股票 {stock_code} 返回空数据，可能是正常情况（停牌、新股等），暂不换代理")
        raise

def validate_daily_data(df):
    """数据校验与清洗"""
    if df.empty:
        return df

    # 创建数据框的副本
    df = df.copy()

    try:
        # 转换日期格式
        if 'trade_date' in df.columns:
            df['trade_date'] = pd.to_datetime(df['trade_date']).dt.date
        elif 'trade_time' in df.columns:
            df['trade_date'] = pd.to_datetime(df['trade_time']).dt.date
        
        # 批量数值类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'pre_close',
                          'change', 'change_pct', 'volume', 'amount', 'turnover_ratio']

        existing_numeric_cols = [col for col in numeric_columns if col in df.columns]
        if existing_numeric_cols:
            df[existing_numeric_cols] = df[existing_numeric_cols].apply(pd.to_numeric, errors='coerce')

        # 删除重复项
        if 'stock_code' in df.columns and 'trade_date' in df.columns:
            df = df.drop_duplicates(subset=['stock_code', 'trade_date'], keep='first')

        # 过滤无效数据
        required_columns = ['stock_code', 'trade_date']
        existing_required_cols = [col for col in required_columns if col in df.columns]
        if existing_required_cols:
            valid_df = df.dropna(subset=existing_required_cols)
        else:
            valid_df = df

        if len(valid_df) < len(df):
            log_progress(logger, f"数据清洗：从 {len(df)} 条减少到 {len(valid_df)} 条有效记录")

        return valid_df

    except Exception as e:
        log_error(logger, f"数据验证失败: {str(e)}")
        raise

def transform_adata_to_ts_format(df, stock_code, stock_name):
    """将adata API返回的数据转换为ts_stock_daily表格式"""
    if df.empty:
        return df
    
    # 创建数据框的副本
    df = df.copy()
    
    # 添加基本信息
    df['stock_code'] = stock_code
    df['name'] = stock_name
    df['ts_code'] = stock_code  # 暂时使用stock_code作为ts_code
    
    # 字段映射
    column_mapping = {
        'change_pct': 'pct_chg',
        'volume': 'vol'
    }
    
    # 重命名列
    for old_col, new_col in column_mapping.items():
        if old_col in df.columns:
            df = df.rename(columns={old_col: new_col})
    
    # 确保必要的列存在
    required_columns = ['ts_code', 'stock_code', 'name', 'trade_date', 
                       'open', 'high', 'low', 'close', 'pre_close',
                       'change', 'pct_chg', 'vol', 'amount']
    
    for col in required_columns:
        if col not in df.columns:
            if col in ['ts_code', 'stock_code', 'name']:
                # 这些列必须有值
                df[col] = ''
            else:
                # 数值列可以设为0
                df[col] = 0.0
    
    # 添加adj_factor列，adata API返回的是前复权数据，设为1.0
    df['adj_factor'] = 1.0
    
    # 只保留需要的列
    df = df[required_columns + ['adj_factor']]
    
    return df

def fetch_stock_data_with_retry(stock_code, start_date, end_date, worker_proxy=None, max_retries=MAX_RETRY_ATTEMPTS):
    """带重试机制的股票日线数据获取函数"""
    last_error = None

    for attempt in range(max_retries):
        try:
            def _get_data():
                start_time = time.time()
                current_proxy = worker_proxy  # 使用工作线程指定的代理

                try:
                    # 使用指定的代理
                    if USE_PROXY and current_proxy:
                        adata.proxy(is_proxy=True, ip=current_proxy)

                    # 调用adata API获取前复权日线数据
                    result = adata.stock.market.get_market(
                        stock_code=stock_code,
                        start_date=start_date,
                        end_date=end_date,
                        k_type=1,  # 日K
                        adjust_type=1  # 前复权
                    )

                    if result is None or result.empty:
                        raise Exception(f"获取股票 {stock_code} 的日线数据返回空数据")

                    # 记录成功
                    response_time = time.time() - start_time
                    if current_proxy:
                        proxy_manager.report_success(current_proxy, response_time)
                        health_monitor.record_request(current_proxy, True, response_time)

                    return result

                except Exception as e:
                    # 记录失败并分析是否需要拉黑代理
                    response_time = time.time() - start_time
                    error_msg = str(e)

                    if current_proxy:
                        # 分析是否需要拉黑代理
                        should_blacklist, blacklist_reason, details = blacklist_detector.analyze_response(
                            current_proxy, exception=e
                        )

                        if should_blacklist:
                            blacklist_detector.record_failure(current_proxy, blacklist_reason, details)
                            proxy_manager.blacklist_proxy(current_proxy, details)
                        else:
                            proxy_manager.report_failure(current_proxy, error_msg)
                            health_monitor.record_request(current_proxy, False, response_time, "api_error", error_msg)

                    raise e

            # 尝试获取数据
            df = retry_api_call(_get_data)
            if df is not None and not df.empty:
                return df, None  # 成功返回数据
            else:
                last_error = f"返回空数据"

        except Exception as e:
            last_error = str(e)
            if attempt < max_retries - 1:  # 不是最后一次重试
                log_progress(logger, f"股票 {stock_code} 第 {attempt + 1} 次尝试失败: {str(e)}，{RETRY_DELAY}秒后重试")
                time.sleep(RETRY_DELAY)
            else:
                log_progress(logger, f"股票 {stock_code} 第 {attempt + 1} 次尝试失败: {str(e)}，已达最大重试次数")

    return None, last_error


def get_stock_daily_data_adata_with_proxy(stock_code, start_date, end_date, worker_proxy=None):
    """使用指定代理获取单只股票的前复权日线数据（保持向后兼容）"""
    df, error = fetch_stock_data_with_retry(stock_code, start_date, end_date, worker_proxy, 1)
    if df is not None:
        return df
    else:
        raise Exception(error or "获取数据失败")


def worker_process_stocks_with_retry_queue(stock_batch, worker_id, start_date, end_date, result_queue, failed_queue, pending_queue):
    """工作线程处理一批股票数据（带重试队列机制）"""
    try:
        log_progress(logger, f"工作线程 {worker_id} 开始处理 {len(stock_batch)} 只股票")

        # 为当前工作线程分配专用代理
        current_proxy = None
        if USE_PROXY and ENABLE_WORKER_PROXY_ISOLATION:
            current_proxy = proxy_manager.get_proxy()
            if current_proxy:
                log_progress(logger, f"工作线程 {worker_id} 分配到专用代理: {current_proxy}")
            else:
                log_error(logger, f"工作线程 {worker_id} 未能获取到代理，将使用无代理模式")

        success_count = 0
        proxy_rotation_count = 0
        pending_count = 0

        for i, (stock_code, stock_name) in enumerate(stock_batch):
            try:
                # 检查是否需要轮换代理
                if (USE_PROXY and ENABLE_WORKER_PROXY_ISOLATION and
                    i > 0 and i % PROXY_ROTATION_INTERVAL == 0):

                    old_proxy = current_proxy
                    current_proxy = proxy_manager.get_proxy()
                    proxy_rotation_count += 1

                    if current_proxy:
                        log_progress(logger, f"工作线程 {worker_id} 第 {proxy_rotation_count} 次代理轮换: {old_proxy} -> {current_proxy} (已处理 {i} 只股票)")
                    else:
                        log_error(logger, f"工作线程 {worker_id} 代理轮换失败，继续使用原代理: {old_proxy}")
                        current_proxy = old_proxy

                # 详细记录API调用开始
                log_progress(logger, f"工作线程 {worker_id} 正在获取股票 {stock_code} 数据 ({i+1}/{len(stock_batch)}) [代理: {current_proxy or '无代理'}]")

                # 使用带重试机制的数据获取函数
                start_time = time.time()
                if ENABLE_WORKER_PROXY_ISOLATION:
                    df, error_msg = fetch_stock_data_with_retry(stock_code, start_date, end_date, current_proxy, MAX_RETRY_ATTEMPTS)
                else:
                    df, error_msg = fetch_stock_data_with_retry(stock_code, start_date, end_date, None, MAX_RETRY_ATTEMPTS)
                response_time = time.time() - start_time

                if df is not None and not df.empty:
                    # 数据转换
                    transformed_df = transform_adata_to_ts_format(df, stock_code, stock_name)

                    # 数据清洗
                    clean_df = validate_daily_data(transformed_df)

                    if not clean_df.empty:
                        result_queue.put(clean_df)
                        success_count += 1
                        log_progress(logger, f"✅ 工作线程 {worker_id} 股票 {stock_code} 成功获取 {len(clean_df)} 条记录，耗时 {response_time:.2f}s")
                    else:
                        # 数据清洗后为空，放入待处理队列
                        pending_queue.put((stock_code, stock_name, "数据清洗后为空"))
                        pending_count += 1
                        log_progress(logger, f"⚠️ 工作线程 {worker_id} 股票 {stock_code} 数据清洗后为空，放入待处理队列")
                else:
                    # 重试3次后仍然失败，放入待处理队列
                    pending_queue.put((stock_code, stock_name, error_msg))
                    pending_count += 1
                    log_progress(logger, f"⚠️ 工作线程 {worker_id} 股票 {stock_code} 重试3次后仍失败，放入待处理队列: {error_msg}")

                # 每处理20只股票报告一次进度
                if (i + 1) % 20 == 0:
                    log_progress(logger, f"工作线程 {worker_id} 已处理 {i+1}/{len(stock_batch)} 只股票，成功 {success_count} 只，待处理 {pending_count} 只，代理轮换 {proxy_rotation_count} 次")

                # 添加延迟，避免过于频繁的请求
                time.sleep(WORKER_DELAY)

            except Exception as e:
                log_error(logger, f"工作线程 {worker_id} 处理股票 {stock_code} 发生异常: {str(e)}")
                pending_queue.put((stock_code, stock_name, str(e)))
                pending_count += 1
                continue

        log_progress(logger, f"工作线程 {worker_id} 完成处理，成功: {success_count}/{len(stock_batch)}，待处理: {pending_count}，代理轮换: {proxy_rotation_count} 次")

        # 释放代理资源
        if current_proxy and USE_PROXY:
            log_progress(logger, f"工作线程 {worker_id} 释放代理: {current_proxy}")

    except Exception as e:
        log_error(logger, f"工作线程 {worker_id} 发生错误: {str(e)}")
        raise


def process_pending_daily_stocks(pending_stocks, start_date, end_date, result_queue, final_failed_queue):
    """处理待处理队列中的股票（最后一次尝试，不浪费IP池）"""
    if not pending_stocks:
        return 0, 0

    log_progress(logger, f"=== 开始处理待处理队列，共 {len(pending_stocks)} 只股票 ===")
    log_progress(logger, f"⚠️  第二轮策略：使用现有代理，不获取新IP，避免浪费IP池资源")

    success_count = 0
    final_failed_count = 0

    # 不获取新代理，使用None（无代理模式）进行最后尝试
    # 这样可以避免浪费IP池资源，因为第二轮失败很可能是股票本身问题
    retry_proxy = None
    log_progress(logger, f"待处理队列使用无代理模式，避免浪费IP池资源")

    for i, (stock_code, stock_name, original_error) in enumerate(pending_stocks):
        try:
            # 检查原错误类型，如果是明显的空数据问题，直接跳过
            if "返回空数据" in original_error:
                final_failed_queue.put((stock_code, stock_name, f"股票可能停牌/退市/无数据: {original_error}"))
                final_failed_count += 1
                log_progress(logger, f"❌ 股票 {stock_code} 跳过处理（可能停牌/退市/无数据）: {original_error}")
                continue

            log_progress(logger, f"最后尝试处理股票 {stock_code} ({i+1}/{len(pending_stocks)})，原错误: {original_error}")

            # 最后一次尝试，不重试，不使用代理
            df, error_msg = fetch_stock_data_with_retry(stock_code, start_date, end_date, retry_proxy, 1)

            if df is not None and not df.empty:
                # 数据转换
                transformed_df = transform_adata_to_ts_format(df, stock_code, stock_name)

                # 数据清洗
                clean_df = validate_daily_data(transformed_df)

                if not clean_df.empty:
                    result_queue.put(clean_df)
                    success_count += 1
                    log_progress(logger, f"✅ 待处理股票 {stock_code} 最终成功获取 {len(clean_df)} 条记录")
                else:
                    final_failed_queue.put((stock_code, stock_name, "数据清洗后为空"))
                    final_failed_count += 1
                    log_progress(logger, f"❌ 股票 {stock_code} 数据清洗后为空，跳过处理")
            else:
                final_failed_queue.put((stock_code, stock_name, error_msg or original_error))
                final_failed_count += 1
                log_progress(logger, f"❌ 股票 {stock_code} 最终失败，跳过处理: {error_msg or original_error}")

            # 添加小延迟
            time.sleep(WORKER_DELAY)

        except Exception as e:
            final_failed_queue.put((stock_code, stock_name, str(e)))
            final_failed_count += 1
            log_error(logger, f"❌ 股票 {stock_code} 最终处理异常，跳过: {str(e)}")

    log_progress(logger, f"=== 待处理队列处理完成，成功: {success_count}，最终失败: {final_failed_count} ===")
    log_progress(logger, f"💡 IP池保护：第二轮未使用任何新代理，节省了IP池资源")
    return success_count, final_failed_count


def worker_process_stocks_with_proxy_rotation(stock_batch, worker_id, start_date, end_date, result_queue, failed_queue):
    """工作线程处理一批股票数据（兼容性包装函数）"""
    # 创建待处理队列
    pending_queue = queue.Queue()

    # 使用新的重试队列机制
    worker_process_stocks_with_retry_queue(stock_batch, worker_id, start_date, end_date, result_queue, failed_queue, pending_queue)

    # 收集待处理的股票
    pending_stocks = []
    while not pending_queue.empty():
        pending_stocks.append(pending_queue.get())

    # 如果有待处理的股票，放入失败队列（将在主函数中统一处理）
    for stock_code, stock_name, error_msg in pending_stocks:
        failed_queue.put((stock_code, stock_name))


def worker_process_stocks(stock_batch, worker_id, start_date, end_date, result_queue, failed_queue):
    """工作线程处理一批股票数据（兼容性包装函数）"""
    if ENABLE_WORKER_PROXY_ISOLATION:
        return worker_process_stocks_with_proxy_rotation(stock_batch, worker_id, start_date, end_date, result_queue, failed_queue)
    else:
        # 原有的简单实现（保持向后兼容）
        log_progress(logger, f"⚠️  工作线程 {worker_id} 使用非隔离模式，每次API调用都会获取新代理IP")
        try:
            log_progress(logger, f"工作线程 {worker_id} 开始处理 {len(stock_batch)} 只股票")

            success_count = 0
            for i, (stock_code, stock_name) in enumerate(stock_batch):
                try:
                    # 获取单只股票的日线数据
                    df = get_stock_daily_data_adata(stock_code, start_date, end_date)

                    if not df.empty:
                        # 数据转换
                        transformed_df = transform_adata_to_ts_format(df, stock_code, stock_name)

                        # 数据清洗
                        clean_df = validate_daily_data(transformed_df)

                        if not clean_df.empty:
                            result_queue.put(clean_df)
                            success_count += 1
                        else:
                            failed_queue.put((stock_code, stock_name))
                    else:
                        failed_queue.put((stock_code, stock_name))

                    # 每处理100只股票报告一次进度
                    if (i + 1) % 100 == 0:
                        log_progress(logger, f"工作线程 {worker_id} 已处理 {i+1}/{len(stock_batch)} 只股票")

                    # 添加小延迟，避免过于频繁的请求
                    time.sleep(WORKER_DELAY)

                except Exception as e:
                    log_error(logger, f"工作线程 {worker_id} 处理股票 {stock_code} 失败: {str(e)}")
                    failed_queue.put((stock_code, stock_name))
                    continue

            log_progress(logger, f"工作线程 {worker_id} 完成处理，成功: {success_count}/{len(stock_batch)}")

        except Exception as e:
            log_error(logger, f"工作线程 {worker_id} 发生错误: {str(e)}")
            raise

def get_and_save_daily_data():
    """获取和保存股票日线数据（支持增量和完整模式）"""
    try:
        # 获取所有股票代码
        stock_codes = get_stock_codes_from_db()

        # 根据模式获取日期范围
        start_date, end_date, target_dates = get_date_range_for_mode()

        # 显示当前运行模式
        mode_name = "增量更新模式" if INCREMENTAL_MODE else "完整历史数据同步模式"
        log_progress(logger, f"=== {mode_name} ===")
        log_progress(logger, f"开始获取日线数据,股票数量: {len(stock_codes)},日期范围: {start_date} 至 {end_date}")
        log_progress(logger, f"重试策略: 每个股票最多重试 {MAX_RETRY_ATTEMPTS} 次，重试间隔 {RETRY_DELAY} 秒")

        if INCREMENTAL_MODE and target_dates:
            # 增量模式：检查现有数据
            log_progress(logger, f"🔍 检查目标日期范围的现有数据...")
            has_existing, existing_count = check_existing_daily_data(target_dates)

            if has_existing:
                log_progress(logger, f"⚠️  发现现有数据: {existing_count} 条记录将被更新")
            else:
                log_progress(logger, f"✅ 目标日期范围无现有数据，将执行新增操作")

        # 第一轮：并发处理所有股票
        log_progress(logger, "=== 第一轮：并发处理所有股票 ===")
        
        # 初始化代理池
        if USE_PROXY:
            log_progress(logger, f"初始化代理池，获取 {CONCURRENT_PROXY_COUNT} 个代理...")
            for _ in range(CONCURRENT_PROXY_COUNT):
                get_new_proxy()
                time.sleep(0.5)  # 避免过于频繁的请求
        
        # 创建结果队列和失败队列
        result_queue = queue.Queue()
        failed_queue = queue.Queue()
        
        # 将股票代码分成多个批次
        stock_batches = []
        for i in range(0, len(stock_codes), STOCKS_PER_WORKER):
            batch = stock_codes[i:i+STOCKS_PER_WORKER]
            stock_batches.append(batch)
        
        log_progress(logger, f"将 {len(stock_codes)} 只股票分成 {len(stock_batches)} 个批次，每个批次 {STOCKS_PER_WORKER} 只股票")
        
        # 使用线程池并发处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=CONCURRENT_WORKERS) as executor:
            futures = []
            
            for i, batch in enumerate(stock_batches):
                future = executor.submit(worker_process_stocks, batch, i+1, start_date, end_date, result_queue, failed_queue)
                futures.append(future)
                
                # 添加小延迟，避免所有线程同时启动
                time.sleep(0.1)
            
            # 等待所有任务完成
            concurrent.futures.wait(futures)
        
        # 收集结果
        all_data = []
        while not result_queue.empty():
            all_data.append(result_queue.get())
        
        # 收集失败的股票
        failed_stocks = []
        while not failed_queue.empty():
            failed_stocks.append(failed_queue.get())
        
        # 第一轮：收集结果
        first_round_data = []
        while not result_queue.empty():
            first_round_data.append(result_queue.get())

        # 收集第一轮失败的股票（这些是待处理的股票）
        pending_stocks = []
        while not failed_queue.empty():
            stock_code, stock_name = failed_queue.get()
            pending_stocks.append((stock_code, stock_name, "第一轮处理失败"))

        first_round_success = len(first_round_data)
        first_round_pending = len(pending_stocks)
        first_round_rate = (first_round_success / len(stock_codes) * 100) if stock_codes else 0

        log_progress(logger, f"第一轮完成: 成功 {first_round_success}/{len(stock_codes)} 只股票 (成功率: {first_round_rate:.1f}%)，待处理 {first_round_pending} 只")

        # 第二轮：处理待处理队列
        final_failed_queue = queue.Queue()
        second_round_success, final_failed_count = process_pending_daily_stocks(pending_stocks, start_date, end_date, result_queue, final_failed_queue)

        # 收集第二轮结果
        second_round_data = []
        while not result_queue.empty():
            second_round_data.append(result_queue.get())

        # 收集最终失败的股票
        final_failed_stocks = []
        while not final_failed_queue.empty():
            final_failed_stocks.append(final_failed_queue.get())

        # 合并所有成功的数据
        all_data = first_round_data + second_round_data
        total_success = len(all_data)
        total_failed = len(final_failed_stocks)
        final_success_rate = (total_success / len(stock_codes) * 100) if stock_codes else 0

        # 详细的完成日志
        log_progress(logger, "=== 最终处理结果 ===")
        log_progress(logger, f"第一轮成功: {first_round_success} 只")
        log_progress(logger, f"第二轮成功: {second_round_success} 只")
        log_progress(logger, f"总成功: {total_success}/{len(stock_codes)} 只 (成功率: {final_success_rate:.1f}%)")
        log_progress(logger, f"最终失败: {total_failed} 只")

        if final_success_rate >= 90:
            log_progress(logger, f"✅ 处理完成，成功率优秀: {final_success_rate:.1f}%")
        elif final_success_rate >= 80:
            log_progress(logger, f"✅ 处理完成，成功率良好: {final_success_rate:.1f}%")
        elif final_success_rate >= 60:
            log_progress(logger, f"⚠️ 处理完成，成功率一般: {final_success_rate:.1f}%")
        else:
            log_error(logger, f"❌ 处理完成，成功率较低: {final_success_rate:.1f}%，建议检查网络或代理配置")

        # 输出最终失败的股票列表
        if final_failed_stocks:
            log_progress(logger, f"最终失败的股票列表 ({len(final_failed_stocks)} 只):")
            for i, (stock_code, stock_name, error_msg) in enumerate(final_failed_stocks[:10]):  # 只显示前10个
                log_progress(logger, f"  {i+1}. {stock_code} ({stock_name}): {error_msg}")
            if len(final_failed_stocks) > 10:
                log_progress(logger, f"  ... 还有 {len(final_failed_stocks) - 10} 只股票失败")

        # 合并所有数据并保存
        if all_data:
            final_df = pd.concat(all_data, ignore_index=True)
            log_progress(logger, f"数据合并完成，共 {len(final_df)} 条记录")

            # 根据模式选择保存方式
            if INCREMENTAL_MODE and target_dates:
                # 增量模式：使用事务安全的增量保存
                log_progress(logger, "使用增量模式保存数据...")
                save_daily_data_incremental(final_df, target_dates)
            else:
                # 完整模式：清空表后保存所有数据
                log_progress(logger, "使用完整模式保存数据...")
                save_daily_data(final_df)

            log_progress(logger, f"所有数据处理完成，成功处理 {len(final_df)} 条记录，失败 {len(final_failed_stocks)} 只股票")
        else:
            log_warning(logger, "未获取到任何有效数据")
        
    except Exception as e:
        log_error(logger, f"数据获取过程发生错误: {str(e)}")
        raise

def save_daily_data(df):
    """保存数据到数据库"""
    import time

    if df.empty:
        log_progress(logger, "无数据需要保存")
        return

    start_time = time.time()
    max_retries = 3
    batch_size = 50000  # 增大批次大小以提高写入速度
    total_records = len(df)
    processed_records = 0

    log_progress(logger, f"开始保存数据到数据库，总记录数: {total_records:,}，批次大小: {batch_size:,}")

    # 先清空表数据
    try:
        with db_connection() as conn:
            conn.execute(text("TRUNCATE TABLE ts_stock_daily"))
            log_progress(logger, "已清空 ts_stock_daily 表数据")
    except Exception as e:
        log_error(logger, f"清空表数据失败: {str(e)}")
        raise

    # 按批次保存数据
    total_batches = (total_records + batch_size - 1) // batch_size

    for i in range(0, total_records, batch_size):
        batch_start_time = time.time()
        batch_df = df.iloc[i:i+batch_size]
        batch_num = i // batch_size + 1
        retry_count = 0

        while retry_count < max_retries:
            try:
                with db_connection() as conn:
                    batch_df.to_sql(
                        'ts_stock_daily',
                        conn,
                        if_exists='append',
                        index=False,
                        method='multi',
                        chunksize=5000  # 增大内部分块大小以提高写入速度
                    )

                    processed_records += len(batch_df)
                    batch_time = time.time() - batch_start_time

                    # 进度报告
                    if batch_num % 5 == 0 or processed_records == total_records:
                        progress_pct = processed_records / total_records * 100
                        elapsed_time = time.time() - start_time
                        avg_speed = processed_records / elapsed_time if elapsed_time > 0 else 0

                        log_progress(logger,
                            f"批次 {batch_num}/{total_batches}: {processed_records:,}/{total_records:,} "
                            f"({progress_pct:.1f}%) - 批次耗时: {batch_time:.2f}s, 平均速度: {avg_speed:.0f}条/秒")

                    break  # 保存成功，跳出重试循环

            except SQLAlchemyError as e:
                retry_count += 1
                if retry_count < max_retries:
                    log_progress(logger, f"批次 {batch_num} 写入失败，第 {retry_count} 次重试: {str(e)}")
                    time.sleep(retry_count)  # 递增等待时间
                else:
                    log_error(logger, f"批次 {batch_num} 写入失败，已重试 {max_retries} 次: {str(e)}")
                    raise

    total_time = time.time() - start_time
    avg_speed = processed_records / total_time if total_time > 0 else 0
    log_progress(logger, f"数据保存完成: {processed_records:,} 条记录，总耗时: {total_time:.2f}秒，平均速度: {avg_speed:.0f}条/秒")


def insert_daily_data_in_transaction(conn, df):
    """在事务中插入日线数据"""
    try:
        if df.empty:
            log_progress(logger, "无数据需要插入")
            return 0

        # 使用pandas的to_sql方法插入数据
        rows_inserted = len(df)
        df.to_sql(
            "ts_stock_daily",
            conn,
            if_exists="append",
            index=False,
            method='multi',
            chunksize=5000
        )

        log_progress(logger, f"✅ 成功插入 {rows_inserted} 条新记录")
        return rows_inserted

    except Exception as e:
        log_error(logger, f"插入数据失败: {str(e)}")
        raise


def save_daily_data_incremental(df, target_dates):
    """增量保存日线数据（事务安全）"""
    if df.empty:
        log_progress(logger, "无数据需要保存")
        return

    log_progress(logger, f"=== 开始增量保存数据 ===")
    log_progress(logger, f"目标日期范围: {len(target_dates)} 个交易日")
    log_progress(logger, f"待保存记录数: {len(df)}")

    if not ENABLE_TRANSACTION_SAFETY:
        log_progress(logger, "⚠️  事务安全已禁用，使用简单插入模式")
        save_daily_data(df)
        return

    try:
        with db_connection() as conn:
            # 开始事务
            trans = conn.begin()

            try:
                log_progress(logger, "🔄 开始数据库事务...")

                # 1. 检查现有数据
                has_existing, existing_count = check_existing_daily_data(target_dates)

                # 2. 删除现有数据（如果存在）
                deleted_count = 0
                if has_existing:
                    log_progress(logger, f"⚠️  发现现有数据 {existing_count} 条，准备删除...")
                    deleted_count = delete_daily_data_by_dates(conn, target_dates)

                # 3. 插入新数据
                log_progress(logger, f"📥 开始插入新数据...")
                inserted_count = insert_daily_data_in_transaction(conn, df)

                # 4. 验证插入的数据
                if inserted_count != len(df):
                    raise Exception(f"插入记录数不匹配: 预期 {len(df)}, 实际 {inserted_count}")

                # 5. 提交事务
                trans.commit()
                log_progress(logger, f"✅ 事务提交成功!")
                log_progress(logger, f"📊 操作摘要: 删除 {deleted_count} 条, 插入 {inserted_count} 条")
                log_progress(logger, f"=== 增量保存完成 ===")

            except Exception as e:
                # 回滚事务
                trans.rollback()
                log_error(logger, f"❌ 事务回滚: {str(e)}")
                log_error(logger, f"💾 数据已回滚到操作前状态，历史数据未受影响")
                raise Exception(f"增量保存失败，已回滚: {str(e)}")

    except Exception as e:
        log_error(logger, f"增量保存过程发生错误: {str(e)}")
        raise

def main():
    """主函数"""
    import time

    start_time = time.time()
    log_progress(logger, "=== 股票日线数据同步(adata API)开始 ===")

    try:
        # 初始化数据库
        init_start = time.time()
        init_database()
        init_time = time.time() - init_start
        log_progress(logger, f"数据库初始化完成，耗时 {init_time:.2f} 秒")

        # 获取和保存日线数据
        data_start = time.time()
        get_and_save_daily_data()
        data_time = time.time() - data_start

        # 总结和性能统计
        total_time = time.time() - start_time
        log_progress(logger, f"=== 股票日线数据同步(adata API)完成 ===")
        log_progress(logger, f"数据处理耗时: {data_time:.2f} 秒，总耗时: {total_time:.2f} 秒")

        # 输出系统性能统计
        log_progress(logger, "=== 系统性能统计 ===")

        # 数据更新模式
        update_mode = "增量更新模式" if INCREMENTAL_MODE else "完整历史数据同步模式"
        log_progress(logger, f"数据更新模式: {update_mode}")

        if INCREMENTAL_MODE:
            log_progress(logger, f"增量回溯天数: {INCREMENTAL_DAYS_BACK}")
            log_progress(logger, f"事务安全: {'启用' if ENABLE_TRANSACTION_SAFETY else '禁用'}")

        # 处理模式
        log_progress(logger, f"并发工作线程: {CONCURRENT_WORKERS}")
        log_progress(logger, f"每线程股票数: {STOCKS_PER_WORKER}")
        log_progress(logger, f"工作线程延迟: {WORKER_DELAY}秒")

        # 重试机制
        log_progress(logger, f"重试机制: 每股票最多 {MAX_RETRY_ATTEMPTS} 次，间隔 {RETRY_DELAY}秒")
        log_progress(logger, f"处理策略: 两轮处理机制（第一轮并发+第二轮待处理队列）")

        # 代理配置
        if USE_PROXY:
            isolation_status = "启用" if ENABLE_WORKER_PROXY_ISOLATION else "禁用"
            log_progress(logger, f"代理隔离模式: {isolation_status}")
            if ENABLE_WORKER_PROXY_ISOLATION:
                log_progress(logger, f"代理轮换间隔: 每 {PROXY_ROTATION_INTERVAL} 只股票")
                log_progress(logger, f"代理分配策略: 每个工作线程独占一个代理IP")
            else:
                log_progress(logger, f"⚠️  警告: 代理隔离已禁用，每次API调用都会获取新代理IP，可能导致频繁的代理请求")

        # 网络配置
        log_progress(logger, f"数据源: adata API")
        network_mode = "代理系统" if USE_PROXY else "本机IP直连"
        log_progress(logger, f"网络模式: {network_mode}")

        # 显示代理池统计
        if USE_PROXY:
            proxy_stats = proxy_manager.get_pool_stats()
            log_progress(logger, f"代理池统计: 总数 {proxy_stats['total_proxies']}, 健康 {proxy_stats['healthy_proxies']}")

        log_progress(logger, "========================")

        return True

    except Exception as e:
        total_time = time.time() - start_time
        error_msg = str(e)
        log_error(logger, f"程序执行异常 (耗时 {total_time:.2f} 秒): {error_msg}")

        # 输出错误信息用于调试
        update_mode = "增量更新" if INCREMENTAL_MODE else "完整历史同步"
        proxy_info = "启用代理" if USE_PROXY else "本机IP"
        proxy_isolation = f"代理隔离={'启用' if ENABLE_WORKER_PROXY_ISOLATION else '禁用'}" if USE_PROXY else ""
        safety_info = f"事务安全={'启用' if ENABLE_TRANSACTION_SAFETY else '禁用'}"
        log_error(logger, f"系统配置: {update_mode}, 并发线程={CONCURRENT_WORKERS}, {proxy_info}, {proxy_isolation}, {safety_info}")

        send_notification(
            message=f"日线数据同步(adata API)失败: {error_msg}",
            title="日线数据同步(adata API)失败",
            tags="日线数据|失败"
        )
        return False

if __name__ == '__main__':
    import sys

    success = main()
    if not success:
        sys.exit(1)