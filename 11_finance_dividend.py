import pandas as pd
import tushare as ts
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
import time
from dotenv import load_dotenv
from contextlib import contextmanager
import pathlib
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification

# 初始化环境变量
load_dotenv()

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# 数据库连接配置
DB_CONFIG = {
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', '31490600'),
    'host': os.getenv('DB_HOST', '**********'),
    'port': os.getenv('DB_PORT', '3306'),
    'database': os.getenv('DB_NAME', 'instockdb'),
    'charset': 'utf8mb4'
}

TUSHARE_TOKEN = os.getenv('TUSHARE_TOKEN', '5aba669a485730beded0d604bd8ccfdaca39f8c9c09ef2d0ed78ed77')

# API访问控制
API_RATE_LIMIT = 300  # 每分钟最大请求次数
API_RATE_INTERVAL = 60  # 时间窗口(秒)

class RateLimiter:
    def __init__(self, limit, interval):
        self.limit = limit
        self.interval = interval
        self.requests = []

    def wait_if_needed(self):
        now = time.time()
        # 清理过期的请求记录（优化：只保留最近的请求）
        cutoff_time = now - self.interval
        self.requests = [req_time for req_time in self.requests if req_time > cutoff_time]

        if len(self.requests) >= self.limit:
            # 等待最早的请求过期
            sleep_time = self.interval - (now - self.requests[0])
            if sleep_time > 0:
                time.sleep(sleep_time)
                # 重新清理过期请求
                now = time.time()
                cutoff_time = now - self.interval
                self.requests = [req_time for req_time in self.requests if req_time > cutoff_time]

        self.requests.append(now)

# 创建限速器实例
rate_limiter = RateLimiter(API_RATE_LIMIT, API_RATE_INTERVAL)

# 表结构定义
TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS `ts_stock_dividend` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `ts_code` VARCHAR(10) NOT NULL COMMENT 'TS代码',
    `stock_code` VARCHAR(6) NOT NULL COMMENT '股票代码',
    `name` VARCHAR(50) NOT NULL COMMENT '股票名称',
    `end_date` DATE COMMENT '分红年度',
    `ann_date` DATE COMMENT '预案公告日',
    `div_proc` VARCHAR(20) COMMENT '实施进度',
    `stk_div` FLOAT COMMENT '每股送转',
    `stk_bo_rate` FLOAT COMMENT '每股送股比例',
    `stk_co_rate` FLOAT COMMENT '每股转增比例',
    `cash_div` FLOAT COMMENT '每股分红(税后)',
    `cash_div_tax` FLOAT COMMENT '每股分红(税前)',
    `record_date` DATE COMMENT '股权登记日',
    `ex_date` DATE COMMENT '除权除息日',
    `pay_date` DATE COMMENT '派息日',
    `div_listdate` DATE COMMENT '红股上市日',
    `imp_ann_date` DATE COMMENT '实施公告日',
    `base_date` DATE COMMENT '基准日',
    `base_share` FLOAT COMMENT '基准股本(万)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY `idx_ts_code` (`ts_code`),
    KEY `idx_stock_code` (`stock_code`),
    KEY `idx_ex_date` (`ex_date`),
    KEY `idx_record_date` (`record_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票分红信息表';
"""

# 全局数据库引擎
_DB_ENGINE = None

def get_db_engine():
    """获取数据库引擎（单例模式）"""
    global _DB_ENGINE
    if _DB_ENGINE is None:
        _DB_ENGINE = create_engine(
            f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
            f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}",
            pool_size=10,
            max_overflow=20,
            pool_recycle=3600,
            pool_pre_ping=True
        )
    return _DB_ENGINE

@contextmanager
def db_connection():
    """数据库连接上下文管理"""
    engine = get_db_engine()
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except SQLAlchemyError as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def init_database():
    """初始化数据库表结构"""
    try:
        with db_connection() as conn:
            conn.execute(text(TABLE_SCHEMA))
            log_progress(logger, "数据库表初始化完成")
    except Exception as e:
        log_error(logger, f"数据库初始化失败: {str(e)}")
        raise

def validate_dividend_data(df, stock_info):
    """数据校验与清洗"""
    if df.empty:
        return df

    # 创建数据框的副本
    df = df.copy()

    # 添加股票代码和名称
    df['stock_code'] = df['ts_code'].str[:6]
    df = pd.merge(df, stock_info[['ts_code', 'name']], on='ts_code', how='left')

    # 批量转换日期格式的列
    date_columns = ['end_date', 'ann_date', 'record_date', 'ex_date',
                   'pay_date', 'div_listdate', 'imp_ann_date', 'base_date']
    existing_date_cols = [col for col in date_columns if col in df.columns]
    if existing_date_cols:
        for col in existing_date_cols:
            df[col] = pd.to_datetime(df[col], format='%Y%m%d', errors='coerce').dt.date

    # 批量数值类型转换
    float_columns = ['stk_div', 'stk_bo_rate', 'stk_co_rate', 'cash_div',
                    'cash_div_tax', 'base_share']
    existing_float_cols = [col for col in float_columns if col in df.columns]
    if existing_float_cols:
        df[existing_float_cols] = df[existing_float_cols].apply(pd.to_numeric, errors='coerce')

    # 删除重复项
    df = df.drop_duplicates(subset=['ts_code', 'end_date', 'div_proc'], keep='first')
    
    # 筛选实施状态的数据 - 注释掉以获取所有状态
    df = df[df['div_proc'] == '实施']
    
    return df.dropna(subset=['ts_code', 'stock_code', 'name'])

def get_dividend_data(end_date, page_size=2000, max_retries=3):
    """
    使用Tushare SDK分页获取指定结束日期的分红送股数据
    
    Args:
        end_date: 结束日期,格式:YYYYMMDD
        page_size: 每页数据量,默认2000条
        max_retries: 最大重试次数
    
    Returns:
        pandas.DataFrame: 包含分红数据的DataFrame,获取失败则返回None
    """
    # 初始化pro接口
    pro = ts.pro_api(TUSHARE_TOKEN)
    all_data = []
    page = 0
    
    # 定义需要获取的字段
    fields = (
        "ts_code,end_date,ann_date,div_proc,"
        "stk_div,stk_bo_rate,stk_co_rate,cash_div,"
        "cash_div_tax,record_date,ex_date,pay_date,"
        "div_listdate,imp_ann_date,base_date,base_share"
    )
    
    while True:
        retry_count = 0
        df_page = None
        
        while retry_count < max_retries:
            try:
                log_progress(logger, f'正在获取 {end_date} 的第 {page + 1} 页数据...')
                log_progress(logger, f'请求参数: end_date={end_date}, limit={page_size}, offset={page * page_size}')
                
                # 使用限速器
                rate_limiter.wait_if_needed()
                
                # 获取数据
                df_page = pro.dividend(
                    end_date=end_date,
                    limit=page_size,
                    offset=page * page_size,
                    fields=fields
                )
                
                # 检查是否获取到数据
                if df_page is None or df_page.empty:
                    log_progress(logger, f'日期 {end_date} 的第 {page + 1} 页没有数据,获取完成')
                    break
                
                log_progress(logger, f'本页获取到 {len(df_page)} 条数据')
                all_data.append(df_page)
                
                # 如果获取的数据量小于页面大小,说明是最后一页
                if len(df_page) < page_size:
                    log_progress(logger, f'日期 {end_date} 的数据不足一页,获取完成')
                    break
                    
                page += 1
                break  # 成功获取数据,跳出重试循环
                
            except Exception as e:
                error_msg = str(e)
                retry_count += 1
                
                if "每分钟最多访问该接口" in error_msg or "抱歉,您每分钟访问次数超出限制" in error_msg:
                    wait_time = 60  # 频率限制错误等待60秒
                    log_progress(logger, f'当前页数: {page + 1}, 已获取数据量: {sum(len(df) for df in all_data)} 条')
                    log_progress(logger, f'触发了频率限制,等待{wait_time}秒后重试...')
                    time.sleep(wait_time)
                    continue
                
                # 其他错误
                log_error(logger, f'获取数据时出错: {error_msg}')
                if retry_count < max_retries:
                    wait_time = 5
                    log_progress(logger, f'等待{wait_time}秒后进行第 {retry_count + 1} 次重试...')
                    time.sleep(wait_time)
                else:
                    log_error(logger, f'已重试 {max_retries} 次仍失败,放弃获取该页数据')
                    # 如果已经获取了一些数据,返回已获取的部分
                    if all_data:
                        log_progress(logger, f'返回已获取的 {sum(len(df) for df in all_data)} 条数据')
                        return pd.concat(all_data, ignore_index=True)
                    return None
        
        # 如果内层循环因为没有数据而退出,则外层循环也退出
        if df_page is None or df_page.empty or len(df_page) < page_size:
            break
    
    if not all_data:
        log_progress(logger, f'未获取到日期 {end_date} 的任何数据')
        return None
        
    # 合并所有数据并返回
    result_df = pd.concat(all_data, ignore_index=True)
    log_progress(logger, f'日期 {end_date} 总共获取到 {len(result_df)} 条数据')
    
    return result_df

def get_and_save_dividend_data():
    """获取和保存分红数据"""
    try:
        # 初始化tushare接口
        ts.set_token(TUSHARE_TOKEN)
        pro = ts.pro_api(TUSHARE_TOKEN)
        
        # 获取所有上市公司列表(用于添加股票名称)
        rate_limiter.wait_if_needed()
        stocks = pro.stock_basic(exchange='', list_status='L', 
                               fields='ts_code,symbol,name')
        
        log_progress(logger, "开始获取分红数据")
        
        # 定义要获取的日期列表
        target_dates = ["20211231", "20221231", "20231231", "20241231", "20240630"]
        
        # 是否是第一批数据(用于决定是否清空表)
        is_first_batch = True
        total_records = 0
        
        # 循环获取每个日期的数据
        for date_idx, end_date in enumerate(target_dates):
            log_progress(logger, f"开始获取 {end_date} 的分红数据 ({date_idx + 1}/{len(target_dates)})")
            
            # 获取该日期的所有分红数据
            df = get_dividend_data(end_date=end_date)
            
            if df is not None and not df.empty:
                # 验证和清洗数据
                clean_df = validate_dividend_data(df, stocks)
                
                if not clean_df.empty:
                    # 保存到数据库
                    save_dividend_data(clean_df, is_first_batch=is_first_batch)
                    is_first_batch = False  # 后续批次不再清空表
                    total_records += len(clean_df)
                    log_progress(logger, f"成功处理 {end_date} 的分红数据,共 {len(clean_df)} 条记录")
                else:
                    log_progress(logger, f"日期 {end_date} 没有符合条件的分红数据")
            else:
                log_progress(logger, f"未获取到日期 {end_date} 的分红数据")
        
        log_progress(logger, f"所有分红数据同步完成,总共保存 {total_records} 条记录")
        
    except Exception as e:
        log_error(logger, f"数据获取过程发生错误: {str(e)}")
        raise

def save_dividend_data(df, is_first_batch=True):
    """安全保存数据到数据库"""
    if df.empty:
        log_progress(logger, "无有效数据需要保存")
        return
    
    try:
        with db_connection() as conn:
            # 使用事务处理
            with conn.begin():
                # 仅在第一批次时清空历史数据
                if is_first_batch:
                    conn.execute(text("TRUNCATE TABLE ts_stock_dividend"))
                    log_progress(logger, "已清空历史数据")
                
                # 批量插入数据
                df.to_sql(
                    name='ts_stock_dividend',
                    con=conn,
                    if_exists='append',
                    index=False,
                    chunksize=1000,
                    method='multi'
                )
            log_progress(logger, f"成功写入 {len(df)} 条记录")
    except SQLAlchemyError as e:
        log_error(logger, f"数据写入失败: {str(e)}")
        raise

if __name__ == '__main__':
    try:
        init_database()
        # 移除开始时的通知
        # send_notification(
        #     message="开始获取股票分红数据...",
        #     title="分红数据同步开始",
        #     tags="分红数据|开始"
        # )
        
        get_and_save_dividend_data()
        
        # 移除成功时的通知
        # send_notification(
        #     message="股票分红数据同步已完成",
        #     title="分红数据同步成功",
        #     tags="分红数据|完成"
        # )
    except Exception as e:
        error_msg = str(e)
        log_error(logger, f"程序执行异常: {error_msg}")
        send_notification(
            message=f"分红数据同步失败: {error_msg}",
            title="分红数据同步异常",
            tags="分红数据|异常"
        )
        exit(1) 