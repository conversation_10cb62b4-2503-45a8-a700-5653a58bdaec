"""
交易日历模块 - 使用adata的trade_calendar接口
Created on 2024-05-30
Updated on 2025-01-17 - 替换为adata API
"""
import pandas as pd
import adata
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
import functools
import time
import sys
from log_config import setup_logger, log_progress, log_error, log_warning

# 初始化环境变量
load_dotenv()

# 获取当前脚本的路径和文件名
current_script_path = os.path.dirname(os.path.abspath(__file__))
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# adata Token
ADATA_TOKEN = os.getenv('ADATA_TOKEN', '')

# 创建一个模拟的market对象，以保持向后兼容性
class MarketMock:
    """模拟adata.stock.market对象，提供向后兼容的API"""
    def get_trade_cal(self, exchange='SSE', start_date=None, end_date=None, is_open=None):
        """模拟旧的get_trade_cal API调用"""
        return get_trade_calendar(exchange, start_date, end_date, is_open)
    
    def get_market(self, stock_code=None, ts_code=None, start_date=None, end_date=None, k_type=None, adjust_type=None, **kwargs):
        """模拟get_market API调用"""
        try:
            # 如果提供了ts_code但没有提供stock_code，则从ts_code中提取stock_code
            if stock_code is None and ts_code is not None:
                if '.' in ts_code:
                    stock_code, _ = ts_code.split('.')
                else:
                    stock_code = ts_code
            
            # 转换日期格式从YYYYMMDD到YYYY-MM-DD（如果需要）
            start_date_formatted = start_date
            end_date_formatted = end_date
            
            if start_date and len(start_date) == 8 and '-' not in start_date:
                start_date_formatted = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
            
            if end_date and len(end_date) == 8 and '-' not in end_date:
                end_date_formatted = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"
            
            # 调用adata API获取周线数据
            df = adata.stock.market.get_market(
                stock_code=stock_code,
                start_date=start_date_formatted,
                end_date=end_date_formatted,
                k_type=k_type if k_type is not None else 2,  # 默认为周K线
                adjust_type=adjust_type if adjust_type is not None else 1  # 默认为前复权
            )
            log_progress(logger, f"成功获取股票 {stock_code} 周线数据: {len(df)}条记录")
            return df
        except Exception as e:
            log_error(logger, f"获取股票 {stock_code} 周线数据失败: {str(e)}")
            return pd.DataFrame()

# 创建模拟对象并添加到adata.stock.market
# 这样其他脚本可以继续使用 adata.stock.market.get_trade_cal 和 adata.stock.market.get_market 而不需要修改
try:
    if not hasattr(adata.stock, 'market'):
        adata.stock.market = type('Market', (), {})()
    if not hasattr(adata.stock.market, 'get_trade_cal'):
        adata.stock.market.get_trade_cal = MarketMock().get_trade_cal
    if not hasattr(adata.stock.market, 'get_market'):
        adata.stock.market.get_market = MarketMock().get_market
except:
    # 如果无法修改adata对象，忽略错误
    pass

# 缓存装饰器
def cache_result(expire_seconds=3600):
    """缓存函数结果的装饰器,有效期为指定秒数"""
    cache = {}
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键
            key = str(args) + str(kwargs)
            
            # 检查缓存是否有效
            if key in cache:
                result, timestamp = cache[key]
                if time.time() - timestamp < expire_seconds:
                    return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache[key] = (result, time.time())
            return result
        
        return wrapper
    
    return decorator

@cache_result(expire_seconds=86400)  # 缓存一天
def get_trade_calendar(exchange='SSE', start_date=None, end_date=None, is_open=None):
    """
    获取交易日历数据 - 使用adata API
    
    Parameters:
    -----------
    exchange : str, 可选
        交易所代码,默认为'SSE'(上海证券交易所)
        可选值:SSE(上交所), SZSE(深交所), CFFEX(中金所),
               SHFE(上期所), CZCE(郑商所), DCE(大商所), INE(上能源)
    start_date : str, 可选
        开始日期,格式为'YYYYMMDD',默认为当年第一天
    end_date : str, 可选
        结束日期,格式为'YYYYMMDD',默认为当年最后一天
    is_open : str, 可选
        是否交易,'0'表示休市,'1'表示交易,默认为None(全部)
        
    Returns:
    --------
    pandas.DataFrame
        包含交易日历信息的DataFrame
    """
    try:
        # 设置默认日期范围为当年
        if start_date is None or end_date is None:
            current_year = datetime.now().year
            if start_date is None:
                start_date = f"{current_year}0101"
            if end_date is None:
                end_date = f"{current_year}1231"
        
        # 转换为datetime对象
        start_dt = datetime.strptime(start_date, '%Y%m%d')
        end_dt = datetime.strptime(end_date, '%Y%m%d')
        
        # 获取所有需要的年份
        years = range(start_dt.year, end_dt.year + 1)
        
        # 收集所有年份的数据
        all_data = []
        for year in years:
            df_year = adata_trade_calendar(year)
            if not df_year.empty:
                # 筛选日期范围内的数据
                df_year = df_year[
                    (df_year['trade_date'] >= start_dt.strftime('%Y-%m-%d')) &
                    (df_year['trade_date'] <= end_dt.strftime('%Y-%m-%d'))
                ]
                all_data.append(df_year)
        
        if not all_data:
            log_warning(logger, f"adata API返回空数据，可能原因：")
            log_warning(logger, f"1. Token无效或过期")
            log_warning(logger, f"2. API调用频率限制")
            log_warning(logger, f"3. 网络连接问题")
            log_warning(logger, f"请检查ADATA_TOKEN环境变量")
            return pd.DataFrame()
        
        # 合并所有年份的数据
        df = pd.concat(all_data, ignore_index=True)
        
        # 根据is_open参数筛选
        if is_open is not None:
            df = df[df['trade_status'] == int(is_open)]
        
        # 添加exchange列以保持与原API兼容
        df['exchange'] = exchange
        
        # 重命名列以保持与原API兼容
        df = df.rename(columns={
            'trade_date': 'cal_date',
            'trade_status': 'is_open'
        })
        
        # 转换日期格式
        df['cal_date'] = df['cal_date'].apply(lambda x: x.replace('-', ''))
        
        log_progress(logger, f"成功获取交易日历数据: {len(df)}条记录")
        return df

    except Exception as e:
        log_error(logger, f"获取交易日历数据失败: {str(e)}")
        log_error(logger, f"请检查：1. ADATA_TOKEN是否正确 2. 网络连接是否正常")
        return pd.DataFrame()  # 返回空DataFrame


def adata_trade_calendar(year):
    """
    使用adata API获取指定年份的交易日历信息
    
    Parameters:
    -----------
    year : int
        年份；例如：2023
        
    Returns:
    --------
    pandas.DataFrame
        包含交易日历信息的DataFrame，包含以下字段：
        - trade_date: 交易日，格式：YYYY-MM-DD
        - trade_status: 交易状态，0表示非交易日，1表示交易日
        - day_week: 一周第几天，从星期天开始计数（1=星期日，7=星期六）
    """
    try:
        # 调用adata API获取交易日历
        df = adata.stock.info.trade_calendar(year=year)
        log_progress(logger, f"成功获取{year}年交易日历数据: {len(df)}条记录")
        return df
        
    except Exception as e:
        log_error(logger, f"获取{year}年交易日历数据失败: {str(e)}")
        return pd.DataFrame()

# 为了向后兼容，添加一个模拟旧API调用的函数
def get_trade_cal(exchange='SSE', start_date=None, end_date=None, is_open=None):
    """
    模拟旧的get_trade_cal API调用，保持向后兼容性
    
    Parameters:
    -----------
    exchange : str, 可选
        交易所代码,默认为'SSE'(上海证券交易所)
    start_date : str, 可选
        开始日期,格式为'YYYYMMDD'
    end_date : str, 可选
        结束日期,格式为'YYYYMMDD'
    is_open : str, 可选
        是否交易,'0'表示休市,'1'表示交易,默认为None(全部)
        
    Returns:
    --------
    pandas.DataFrame
        包含交易日历信息的DataFrame，格式与旧API一致
    """
    return get_trade_calendar(exchange, start_date, end_date, is_open)

def is_trade_day(date=None, exchange='SSE'):
    """
    判断给定日期是否为交易日
    
    Parameters:
    -----------
    date : datetime.date, datetime.datetime 或 str, 可选
        需要判断的日期,可以是datetime对象或'YYYY-MM-DD'格式的字符串
        如果为None,则默认为当天
    exchange : str, 可选
        交易所代码,默认为'SSE'(上海证券交易所)
        
    Returns:
    --------
    bool
        True表示是交易日,False表示非交易日
    """
    try:
        # 处理日期参数
        if date is None:
            date = datetime.now().date()
        elif isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()
        elif isinstance(date, datetime):
            date = date.date()
            
        # 获取该年份的交易日历
        year = date.year
        calendar_df = adata_trade_calendar(year)
        
        # 判断是否为交易日
        if calendar_df.empty:
            log_warning(logger, f"未找到 {year} 年的交易日历数据")
            return False
            
        # 转换日期格式进行比较
        date_str = date.strftime('%Y-%m-%d')
        day_data = calendar_df[calendar_df['trade_date'] == date_str]
        
        if day_data.empty:
            log_warning(logger, f"未找到日期 {date_str} 的交易日历数据")
            return False
            
        trade_status = day_data.iloc[0]['trade_status']
        return trade_status == 1
        
    except Exception as e:
        log_error(logger, f"判断交易日失败: {str(e)}")
        return False

def get_latest_trade_day(date=None, exchange='SSE'):
    """
    获取给定日期当天或之前的最近一个交易日
    
    Parameters:
    -----------
    date : datetime.date, datetime.datetime 或 str, 可选
        参考日期,可以是datetime对象或'YYYY-MM-DD'格式的字符串
        如果为None,则默认为当天
    exchange : str, 可选
        交易所代码,默认为'SSE'(上海证券交易所)
        
    Returns:
    --------
    datetime.date
        最近的交易日日期
    """
    try:
        # 处理日期参数
        if date is None:
            date = datetime.now().date()
        elif isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()
        elif isinstance(date, datetime):
            date = date.date()
            
        # 如果当天是交易日,直接返回
        if is_trade_day(date, exchange):
            return date
            
        # 获取当年的交易日历
        year = date.year
        calendar_df = adata_trade_calendar(year)
        
        if calendar_df.empty:
            log_error(logger, f"未找到{year}年的交易日历数据")
            return date
            
        # 筛选交易日
        trade_days_df = calendar_df[calendar_df['trade_status'] == 1]
        
        # 将日期转换为字符串格式进行比较
        date_str = date.strftime('%Y-%m-%d')
        
        # 找到小于等于给定日期的最大交易日
        trade_days = trade_days_df[trade_days_df['trade_date'] <= date_str]['trade_date']
        
        if trade_days.empty:
            log_error(logger, f"未找到{date_str}之前的交易日")
            # 获取上一年的最后一个交易日
            prev_year = year - 1
            prev_calendar = adata_trade_calendar(prev_year)
            
            if not prev_calendar.empty:
                prev_trade_days = prev_calendar[prev_calendar['trade_status'] == 1]
                if not prev_trade_days.empty:
                    latest_date_str = prev_trade_days['trade_date'].max()
                    return datetime.strptime(latest_date_str, '%Y-%m-%d').date()
            return date
            
        latest_date_str = trade_days.max()
        return datetime.strptime(latest_date_str, '%Y-%m-%d').date()
        
    except Exception as e:
        log_error(logger, f"获取最近交易日失败: {str(e)}")
        return date

def get_next_trade_day(date=None, exchange='SSE'):
    """
    获取给定日期之后的下一个交易日
    
    Parameters:
    -----------
    date : datetime.date, datetime.datetime 或 str, 可选
        参考日期,可以是datetime对象或'YYYY-MM-DD'格式的字符串
        如果为None,则默认为当天
    exchange : str, 可选
        交易所代码,默认为'SSE'(上海证券交易所)
        
    Returns:
    --------
    datetime.date
        下一个交易日日期
    """
    try:
        # 处理日期参数
        if date is None:
            date = datetime.now().date()
        elif isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()
        elif isinstance(date, datetime):
            date = date.date()
            
        # 获取当年及下一年的交易日历(考虑年底情况)
        year = date.year
        current_calendar = adata_trade_calendar(year)
        next_calendar = adata_trade_calendar(year + 1)
        
        # 合并两年的交易日历
        if current_calendar.empty and next_calendar.empty:
            log_error(logger, f"未找到{year}-{year+1}年的交易日历数据")
            return date + timedelta(days=1)
        
        # 筛选交易日
        all_trade_days = pd.DataFrame()
        if not current_calendar.empty:
            current_trade_days = current_calendar[current_calendar['trade_status'] == 1]
            all_trade_days = pd.concat([all_trade_days, current_trade_days], ignore_index=True)
        
        if not next_calendar.empty:
            next_trade_days = next_calendar[next_calendar['trade_status'] == 1]
            all_trade_days = pd.concat([all_trade_days, next_trade_days], ignore_index=True)
        
        if all_trade_days.empty:
            log_error(logger, f"未找到{year}-{year+1}年的交易日数据")
            return date + timedelta(days=1)
            
        # 将日期转换为字符串格式进行比较
        date_str = date.strftime('%Y-%m-%d')
        
        # 找到大于给定日期的最小交易日
        trade_days = all_trade_days[all_trade_days['trade_date'] > date_str]['trade_date']
        
        if trade_days.empty:
            log_error(logger, f"未找到{date_str}之后的交易日")
            return date + timedelta(days=1)
            
        next_date_str = trade_days.min()
        return datetime.strptime(next_date_str, '%Y-%m-%d').date()
        
    except Exception as e:
        log_error(logger, f"获取下一交易日失败: {str(e)}")
        return date + timedelta(days=1)

def get_friday_of_current_week(date):
    """
    获取指定日期所在周的周五日期
    如果当前日期是周六或周日，返回刚刚过去的周五
    
    参数:
    -----------
    date : datetime.date
        要查找对应周五的日期
        
    返回:
    --------
    datetime.date
        同一周的周五日期（如果是周六日则是刚过去的周五）
    """
    # 计算到周五的天数 (weekday 4 = 周五)
    weekday = date.weekday()
    if weekday < 5:  # 周一到周五
        days_to_friday = 4 - weekday
    else:  # 周六或周日，返回刚刚过去的周五
        days_to_friday = -(weekday - 4)
    
    friday = date + timedelta(days=days_to_friday)
    return friday

if __name__ == "__main__":
    # 测试交易日历功能
    today = datetime.now().date()
    
    # 测试adata API直接调用
    try:
        current_year = today.year
        df = adata_trade_calendar(current_year)
        if not df.empty:
            log_progress(logger, f"adata API直接调用成功，获取{current_year}年交易日历数据: {len(df)}条记录")
            # 显示前5条数据
            log_progress(logger, f"前5条数据示例:\n{df.head()}")
        else:
            log_error(logger, "adata API直接调用失败: 返回空数据")
    except Exception as e:
        log_error(logger, f"adata API直接调用失败: {str(e)}")
    
    # 测试是否为交易日
    is_today_trade = is_trade_day(today)
    log_progress(logger, f"今天 {today} {'是' if is_today_trade else '不是'}交易日")
    
    # 获取最近的交易日
    latest_trade_day = get_latest_trade_day(today)
    log_progress(logger, f"最近交易日是: {latest_trade_day}")
    
    # 获取下一个交易日
    next_trade_day = get_next_trade_day(today)
    log_progress(logger, f"下一个交易日是: {next_trade_day}")
    
    # 获取交易日历数据示例
    try:
        df = get_trade_calendar(start_date='20240101', end_date='20240131')
        if not df.empty and 'is_open' in df.columns:
            trade_days_count = len(df[df['is_open'] == 1])
            log_progress(logger, f"2024年1月共有 {trade_days_count} 个交易日")
        else:
            log_error(logger, "获取交易日历示例失败: 数据为空或缺少必要列")
    except Exception as e:
        log_error(logger, f"获取交易日历示例失败: {str(e)}")
    
    # 测试adata API示例 - 根据用户提供的示例
    try:
        test_year = 2023
        df_example = adata.stock.info.trade_calendar(year=test_year)
        if not df_example.empty:
            log_progress(logger, f"adata API示例调用成功，获取{test_year}年交易日历数据: {len(df_example)}条记录")
            # 显示前5条数据
            log_progress(logger, f"示例前5条数据:\n{df_example.head()}")
            
            # 验证数据格式
            required_columns = ['trade_date', 'trade_status', 'day_week']
            if all(col in df_example.columns for col in required_columns):
                log_progress(logger, "数据格式验证通过，包含所需字段")
            else:
                log_warning(logger, f"数据格式验证失败，缺少字段: {set(required_columns) - set(df_example.columns)}")
        else:
            log_error(logger, "adata API示例调用失败: 返回空数据")
    except Exception as e:
        log_error(logger, f"adata API示例调用失败: {str(e)}")