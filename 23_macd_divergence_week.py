import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text, String
import os
# 使用集中式日志配置
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification
from multiprocessing import Pool
import multiprocessing
from functools import lru_cache
import argparse
from trade_calendar import is_trade_day, get_latest_trade_day, get_friday_of_current_week

# 获取当前脚本的文件名
script_name = os.path.splitext(os.path.basename(__file__))[0]
# 初始化日志系统
logger = setup_logger(script_name)

# 将process_group函数移到类外部
def process_stock_group(group_data):
    """处理单个股票组的数据"""
    code = group_data['code'].iloc[0]
    row = {'code': code}
    
    # 计算最近10周的最小值
    recent_10 = group_data[group_data['rn'] <= 10].copy()
    if not recent_10.empty:
        # 检查是否有有效的MACD值（非NaN）
        valid_macd_10 = recent_10['macd'].dropna()
        if not valid_macd_10.empty:
            min_idx = valid_macd_10.idxmin()
            row.update({
                'diff10_min': recent_10.loc[min_idx, 'macd'],
                'diff10_date': recent_10.loc[min_idx, 'date'],
                'df_10_td': recent_10.loc[min_idx, 'rn']
            })
        else:
            # 如果所有MACD值都是NaN，设置为None
            row.update({
                'diff10_min': None,
                'diff10_date': None,
                'df_10_td': None
            })
    
    # 计算各组最小值
    for i, (group_name, group_subset) in enumerate(
        group_data.groupby('group', observed=True), 1
    ):
        if not group_subset.empty:
            # 检查是否有有效的MACD值（非NaN）
            valid_macd = group_subset['macd'].dropna()
            if not valid_macd.empty:
                min_idx = valid_macd.idxmin()
                row.update({
                    f'diff_min_{i}': group_subset.loc[min_idx, 'macd'],
                    f'diff{i}_date': group_subset.loc[min_idx, 'date'],
                    f'diff{i}_price': group_subset.loc[min_idx, 'close'],
                    f'rn_{i}': group_subset.loc[min_idx, 'rn'] if i == 1 else None
                })
            else:
                # 如果所有MACD值都是NaN，设置为None
                row.update({
                    f'diff_min_{i}': None,
                    f'diff{i}_date': None,
                    f'diff{i}_price': None,
                    f'rn_{i}': None
                })
    
    return row

class MacdWeeklyDivergenceAnalyzer:
    def __init__(self, db_url="mysql+pymysql://root:31490600@10.10.10.2:3306/instockdb"):
        self.db_url = db_url
        self.engine = create_engine(
            db_url,
            pool_size=5,          # 连接池大小
            max_overflow=10,      # 超过pool_size后最多可以创建的连接数
            pool_timeout=30,      # 连接池获取连接的超时时间
            pool_recycle=1800,    # 连接重置时间(30分钟)
            pool_pre_ping=True    # 连接前ping一下，确保连接有效
        )
        
    def get_base_data(self, days=60, analysis_date=None):
        """获取基础数据,相当于SQL中的base_date CTE"""
        try:
            # 构建基础数据查询 - 移除不需要的列检查
            column_list = """
                date, code, name, close, macd, macds, ma26,
                ma_26d, diff_26d,
                macd_diff1, macd_diff2
            """
            
            # 如果指定了分析日期，则使用该日期
            date_filter = ""
            if analysis_date:
                date_filter = f"AND date <= '{analysis_date}'"
                
            query = f"""
            WITH RECURSIVE dates AS (
                SELECT MAX(date) as date FROM cn_stock_indicators_weekly
                WHERE 1=1 {date_filter}
                UNION ALL
                SELECT (
                    SELECT MAX(date) 
                    FROM cn_stock_indicators_weekly 
                    WHERE date < d.date
                )
                FROM dates d
                WHERE d.date IS NOT NULL
                LIMIT {days}
            ),
            base_data AS (
                SELECT 
                    {column_list},
                    ROW_NUMBER() OVER (PARTITION BY code ORDER BY date DESC) as rn
                FROM cn_stock_indicators_weekly
                WHERE date IN (SELECT date FROM dates WHERE date IS NOT NULL)
            )
            SELECT * FROM base_data WHERE rn <= {days}
            ORDER BY code, date DESC
            """
            return pd.read_sql(query, self.engine)
        except Exception as e:
            log_error(logger, f"获取周线基础数据时出错: {str(e)}")
            return pd.DataFrame()

    def calculate_groups(self, df):
        """将数据分组,相当于SQL中的group_number CTE"""
        df['group'] = pd.cut(df['rn'], 
                           bins=[0, 20, 40, 60], 
                           labels=['group1', 'group2', 'group3'],
                           right=True)
        return df

    def calculate_min_values(self, df):
        """使用多进程优化最小值计算"""
        # 将数据分成多个组
        groups = [group for _, group in df.groupby('code')]
        
        # 使用进程池并行处理
        with Pool(processes=multiprocessing.cpu_count()) as pool:
            results = pool.map(process_stock_group, groups)
        
        return pd.DataFrame(results)

    def format_numeric_columns(self, df):
        """格式化数值列以匹配数据库表结构"""
        # 批量格式化数值列
        format_rules = {
            2: ['close', 'diff1_price', 'diff2_price'],  # double(20,2)
            5: ['diff_1', 'diff_2', 'ma_26d', 'diff_26d', 'macd_diff1', 'macd_diff2', 'diff10_min']  # double(22,5)
        }

        for decimals, columns in format_rules.items():
            existing_cols = [col for col in columns if col in df.columns]
            if existing_cols:
                df[existing_cols] = df[existing_cols].round(decimals)

        return df

    def find_divergence(self, df, min_values):
        """寻找底背离"""
        latest = df[df['rn'] == 1].copy()
        result = pd.merge(latest, min_values, on='code', how='left')
        
        # 计算回调信号
        # 恢复原计算方式，同时使用ma_26d和diff_26d字段
        result['huitiao'] = np.where(
            (result['ma_26d'] > 0) & (result['diff_26d'] < 0),
            1, 0
        )
        
        # 计算底背离信号
        result['jd'] = np.where(
            (result['diff1_price'] < result['diff2_price']) &
            (result['diff_min_1'] > result['diff_min_2']) &
            (result['diff_min_1'] < 0) &
            (result['diff_min_2'] < 0),
            1, 0
        )
        
        # 计算涨跌幅
        def calculate_dmd(row):
            if pd.isna(row['diff1_price']) or row['diff1_price'] == 0:
                return '0%'
            return f"{((row['close'] / row['diff1_price'] - 1) * 100):.0f}%"
        
        result['dmd'] = result.apply(calculate_dmd, axis=1)
        
        # 使用rn_1作为d_td
        result['d_td'] = result['rn_1']
        
        return result

    def save_to_history(self, df, analysis_date=None):
        """将分析结果保存到周线历史表"""
        try:
            # 批量格式化日期列
            date_cols = ['date', 'diff1_date', 'diff2_date', 'diff10_date']
            for col in date_cols:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col]).dt.strftime('%Y%m%d')
            
            # 批量格式化数值列
            df = self.format_numeric_columns(df)
            
            # 批量转换整数列
            int_cols = ['jd', 'huitiao', 'd_td', 'df_10_td']
            for col in int_cols:
                if col in df.columns:
                    df[col] = df[col].astype('Int64')
            
            # 检查目标表结构
            with self.engine.connect() as conn:
                # 获取表列信息
                table_columns_query = text("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = 'macd_analy_weekly_his'
                """)
                table_columns = [row[0] for row in conn.execute(table_columns_query).fetchall()]
                
                # 确保只使用表中存在的列
                df_columns = set(df.columns)
                valid_columns = [col for col in df.columns if col.lower() in [c.lower() for c in table_columns]]
                missing_columns = df_columns - set(valid_columns)
                
                if missing_columns:
                    log_warning(logger, f"以下列在macd_analy_weekly_his表中不存在，将被忽略: {', '.join(missing_columns)}")
                    df = df[valid_columns]
            
            # 使用事务批量删除和插入
            with self.engine.begin() as conn:
                # 获取最新日期并删除记录
                latest_date = df['date'].iloc[0]
                conn.execute(
                    text("DELETE FROM macd_analy_weekly_his WHERE date = :date"),
                    {"date": latest_date}
                )
                
                # 使用更大的chunksize批量插入
                df.to_sql(
                    'macd_analy_weekly_his',
                    conn,
                    if_exists='append',
                    index=False,
                    method='multi',
                    chunksize=5000,  # 增加chunksize
                    dtype={
                        'date': String(8),
                        'code': String(6),
                        'name': String(20),
                        'dmd': String(18)
                    }
                )
            
            log_progress(logger, f"已成功保存 {len(df)} 条周线分析记录到历史表")
            
        except Exception as e:
            log_error(logger, f"保存周线数据时发生错误: {str(e)}")
            raise

    def process_data(self, analysis_date=None):
        """处理周线数据并返回结果"""
        try:
            # 1. 获取基础数据
            df = self.get_base_data(days=60, analysis_date=analysis_date)
            if df.empty:
                raise ValueError("未获取到周线基础数据")
            
            # 获取最新日期
            latest_date = df['date'].max()
            logger.info(f"分析的最新周线数据日期: {latest_date}")
            
            # 2. 计算分组
            df = self.calculate_groups(df)
            
            # 3. 计算最小值
            min_values = self.calculate_min_values(df)
            
            # 4. 寻找底背离
            result = self.find_divergence(df, min_values)
            
            # 5. 选择并重命名最终结果列,确保与表结构匹配
            
            # 需要重命名的列映射（只包含需要重命名的列）
            column_renames = {
                'diff_min_1': 'diff_1',
                'diff_min_2': 'diff_2'
            }

            # 需要保留的列（按顺序）
            required_columns = [
                'date', 'code', 'name', 'close', 'diff_min_1', 'diff_min_2',
                'diff1_date', 'diff2_date', 'diff1_price', 'diff2_price',
                'jd', 'd_td', 'ma_26d', 'diff_26d', 'huitiao',
                'macd_diff1', 'macd_diff2', 'dmd', 'diff10_min', 'diff10_date', 'df_10_td'
            ]

            # 只选择存在的列
            available_columns = [col for col in required_columns if col in result.columns]
            final_result = result[available_columns].copy()

            # 重命名需要重命名的列
            final_result = final_result.rename(columns=column_renames)
            
            # 只保留最新日期的数据
            final_result = final_result[final_result['date'] == latest_date]
            
            # 6. 保存到历史表
            self.save_to_history(final_result)
            
            return final_result
            
        except Exception as e:
            log_error(logger, f"处理周线数据时发生错误: {str(e)}")
            send_notification(
                message=f"周线MACD底背离分析失败: {str(e)}",
                title="周线MACD底背离分析失败",
                tags="周线MACD|错误"
            )
            return None

    @lru_cache(maxsize=128)
    def get_latest_trade_date(self):
        """缓存最新交易日期"""
        with self.engine.connect() as conn:
            result = conn.execute(text("SELECT MAX(date) FROM cn_stock_indicators_weekly"))
            return result.scalar()

def get_latest_weekly_trade_date(analyzer=None):
    """
    从ts_stock_weekly表中获取最新的交易日期

    Parameters:
    -----------
    analyzer : MacdWeeklyDivergenceAnalyzer
        分析器实例，复用其数据库连接

    Returns:
    --------
    datetime.date
        最新的交易日期
    """
    try:
        # 复用analyzer的数据库连接，避免重复创建
        if analyzer is None:
            analyzer = MacdWeeklyDivergenceAnalyzer()

        query = "SELECT MAX(trade_date) FROM ts_stock_weekly"
        with analyzer.engine.connect() as conn:
            result = conn.execute(text(query))
            latest_date = result.scalar()
            if latest_date:
                logger.info(f"从ts_stock_weekly表获取到的最新交易日期: {latest_date}")
                return latest_date
            else:
                log_warning(logger, "未从ts_stock_weekly表获取到交易日期，将使用交易日历")
                return None
    except Exception as e:
        log_error(logger, f"获取ts_stock_weekly最新日期时出错: {str(e)}")
        return None

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='计算股票周线MACD底背离')
    parser.add_argument('--force', action='store_true', help='强制使用最近交易日的数据进行计算')
    parser.add_argument('--date', type=str, help='指定计算日期,格式为YYYY-MM-DD')
    args = parser.parse_args()
    
    # 获取当天日期
    today = datetime.now().date()
    
    # 确定要使用的日期
    if args.date:
        try:
            calc_date = datetime.strptime(args.date, '%Y-%m-%d').date()
            logger.info(f"使用指定日期: {calc_date}")
        except ValueError:
            log_error(logger, f"日期格式错误: {args.date},应为YYYY-MM-DD格式")
            exit(1)
    else:
        # 创建analyzer实例以复用数据库连接
        analyzer = MacdWeeklyDivergenceAnalyzer()

        # 尝试从ts_stock_weekly表中获取最新交易日期
        latest_db_date = get_latest_weekly_trade_date(analyzer)

        if latest_db_date and not args.force:
            calc_date = latest_db_date
            logger.info(f"使用ts_stock_weekly表中的最新日期: {calc_date}")
        else:
            # 如果未获取到数据库日期或强制使用交易日历，则使用交易日历逻辑
            logger.info("未获取到数据库日期或强制使用交易日历，将使用交易日历逻辑")
            
            # 检查今天是否为交易日
            is_today_trade = is_trade_day(today)
            logger.info(f"今天 {today} {'是' if is_today_trade else '不是'}交易日")
            
            # 获取本周五的日期
            friday_date = get_friday_of_current_week(today)
            logger.info(f"本周五的日期是: {friday_date}")
            
            # 检查本周五是否为交易日
            is_friday_trade_day = is_trade_day(friday_date)
            logger.info(f"本周五 {friday_date} {'是' if is_friday_trade_day else '不是'}交易日")
            
            # 获取最近的交易日
            latest_trade_day = get_latest_trade_day(today)
            logger.info(f"最近的交易日是: {latest_trade_day}")
            
            # 如果本周五是交易日，使用本周五；否则使用最近的交易日
            if is_friday_trade_day and not args.force:
                calc_date = friday_date
                logger.info(f"将使用本周五 {calc_date} 的数据")
            else:
                calc_date = latest_trade_day
                logger.info(f"将使用最近交易日 {calc_date} 的数据进行计算")
    
    # 转换日期格式为字符串
    analysis_date = calc_date.strftime('%Y-%m-%d')

    logger.info("开始周线MACD底背离分析...")
    # 如果analyzer还未创建，则创建一个
    if 'analyzer' not in locals():
        analyzer = MacdWeeklyDivergenceAnalyzer()
    result = analyzer.process_data(analysis_date=analysis_date)
    
    if result is not None:
        divergence_count = len(result[result['jd'] == 1])
        huitiao_count = len(result[result['huitiao'] == 1])
        
        msg = (f"周线MACD分析完成\n"
               f"发现 {divergence_count} 个底背离信号\n"
               f"发现 {huitiao_count} 个回调信号\n"
               f"分析日期: {analysis_date}\n"
               f"总计分析 {len(result)} 只股票")
        
        logger.info(msg)
        # 改用log_config模块中的send_notification函数，但保持注释状态
        # send_notification(
        #    message=msg,
        #    title="周线MACD分析完成",
        #    tags="周线MACD|完成"
        # )

if __name__ == "__main__":
    main() 